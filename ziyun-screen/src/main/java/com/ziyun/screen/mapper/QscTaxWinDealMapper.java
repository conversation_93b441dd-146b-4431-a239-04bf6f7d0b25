package com.ziyun.screen.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ziyun.common.annotation.DataColumn;
import com.ziyun.common.annotation.DataPermission;
import com.ziyun.screen.domain.QscTaxHallOverview;
import com.ziyun.screen.domain.QscTaxWinDeal;
import com.ziyun.screen.domain.vo.QscTaxHallOverviewVo;
import com.ziyun.screen.domain.vo.QscTaxWinDealVo;
import com.ziyun.common.core.mapper.BaseMapperPlus;
import org.apache.ibatis.annotations.Param;

/**
 * 窗口办Mapper接口
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
public interface QscTaxWinDealMapper extends BaseMapperPlus<QscTaxWinDealMapper, QscTaxWinDeal, QscTaxWinDealVo> {
    @DataPermission({
        @DataColumn(key = "deptName", value = "dept_id"),
        @DataColumn(key = "userName", value = "user_id")
    })
    Page<QscTaxWinDealVo> customPageList(@Param("page") Page<QscTaxWinDeal> page, @Param("ew") Wrapper<QscTaxWinDeal> wrapper);
}
