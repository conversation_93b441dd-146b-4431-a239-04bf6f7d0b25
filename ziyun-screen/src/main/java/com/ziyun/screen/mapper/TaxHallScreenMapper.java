package com.ziyun.screen.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.ziyun.common.core.mapper.BaseMapperPlus;
import com.ziyun.screen.domain.vo.halldata.*;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

public interface TaxHallScreenMapper extends BaseMapperPlus {

    /**
     * 查询业务分析
     * @return
     */
    @Select("SELECT b.NAME as leftText, COUNT(*) num, cast((select COUNT(*) from ticket) as int) as 'max'\n" +
        "FROM ticket t\n" +
        "RIGHT JOIN business b on t.BIZ_UID = b.UID\n" +
        "AND T.STATUS !=6\n" +
        "GROUP BY b.NAME\n" +
        "order by num DESC")
    List<BizThingAnalysisVo> queryBizThingAnalysis();


    /**
     * 查询窗口办理
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    @Select("SELECT e.NAME AS empName, w.NAME AS winName, COUNT(1) AS dealPersonCount\n" +
        "\t, COUNT(1) AS dealBizCount\n" +
        "\t, SUM(CASE \n" +
        "\t\tWHEN t.RANK = 11 THEN 1\n" +
        "\t\tELSE 0\n" +
        "\tEND) / COUNT(t.RANK) * 100 AS bestRankRate\n" +
        "\t, AVG(DATEDIFF(mi, t.START_TIME, t.END_TIME)) AS dealTime\n" +
        "\t, AVG(DATEDIFF(mi, t.START_TIME, t.END_TIME)) AS dealTimeAvg\n" +
        "\t, MAX(DATEDIFF(mi, t.START_TIME, t.END_TIME)) AS dealTimeMax\n" +
        "FROM ticket t\n" +
        "\tINNER JOIN employee e ON t.EMP_UID = e.UID\n" +
        "\tINNER JOIN window w ON w.UID = t.WIN_ID\n" +
        "WHERE e.STATUS != 6\n" +
        "GROUP BY e.NAME, w.NAME;")
    List<ThingsDealTableVo> thingsDealTableAnalysis();


    /**
     * 业务走势
     * @return
     */
    @Select("select convert(varchar(5),Min(START_TIME),110) xAxis, count(*) barY from ticket_log where datediff(day,START_TIME,getdate())<=5\n" +
        "group by datediff(day,START_TIME,getdate())\n" +
        "order by Min(START_TIME) asc")
    List<BizBarChartVo> queryBizBarChartVos();

    @Select("SELECT convert(varchar(5),Min(START_TIME),110) xAxis, count(*) barY \n" +
        "FROM ticket_log \n" +
        "WHERE datediff(day,START_TIME,getdate())<=15 \n" +
        "GROUP BY datediff(day,START_TIME,getdate()) \n" +
        "ORDER BY Min(START_TIME) asc")
    List<BizBarChartVo> DmBizBarChartVos();

    /**
     *
     * @return
     */
    @Select("SELECT avg(a.time) dealTimeAvg,max(a.time) dealTimeMax,COUNT(1) dealThingsCount\n" +
        "from (select *,DATEDIFF(mi,t.START_TIME,t.END_TIME)  as time from ticket t) a\n" +
        "where a.STATUS!=6")
    WinDealInfoVo queryWinDealInfoVo();

    @Select("SELECT s.NAME as name, COUNT(*) value\n" +
        "FROM ticket t\n" +
        "RIGHT JOIN business s on t.BIZ_UID = s.UID\n" +
        "AND t.STATUS !=6\n" +
        "GROUP BY s.NAME\n" +
        "order by value DESC;")
    List<WinBizDealPieVo> queryWinBizDealPieVo();

    @Select("SELECT w.NAME as winName, COUNT(*) finishCount, avg(a.time) dealTimeAvg, max(a.time) dealTimeMax\n" +
        "FROM (select *,DATEDIFF(n,t.START_TIME,t.END_TIME)  as time from ticket t) a\n" +
        "RIGHT JOIN window w on a.win_ID = w.UID\n" +
        "AND a.STATUS !=6\n" +
        "GROUP BY w.NAME\n" +
        "order by finishCount DESC;")
    List<WinDealTableVo> queryWinDealTableVo();

    /**
     * 等待情况分析
     */
    @Select("select\n" +
        "       avg(nt.DIFF_TKT_DATE_TIME) waitingTimeAvg,\n" +
        "       max(nt.DIFF_TKT_DATE_TIME) waitingTimeMax,\n" +
        "       count(case when(nt.STATUS = 0 ) then 1 else null end) waitingPeopleCount,\n" +
        "       count(case when(nt.DIFF_TKT_DATE_TIME<=20) then 1 else null end) less,\n" +
        "       count(case when(20< nt.DIFF_TKT_DATE_TIME AND nt.DIFF_TKT_DATE_TIME<=30) then 1 else null end) inBetween,\n" +
        "       count(case when(nt.DIFF_TKT_DATE_TIME>30) then 1 else null end) greater\n" +
        "from\n" +
        "    (select\n" +
        "            DATEDIFF(mi,cast(convert(varchar(30), TKT_DATE,23) +' '+ convert(varchar(30), TKT_TIME,24) AS datetime),START_TIME) DIFF_TKT_DATE_TIME,\n" +
        "            TKT_DATE,TKT_TIME,START_TIME,STATUS\n" +
        "    from ticket where STATUS != 6\n" +
        "    group by TKT_DATE,TKT_TIME,START_TIME,STATUS) nt")
    WaitingAnalysisVo waitingAnalysis();

    /**
     * 每日人流走势
     */
    @Select("select xAxis, count(TKT_TIME) dataY  from ticket right join (SELECT SUBSTRING(CONVERT(CHAR(32),DATEADD(mi,number,'08:00'),108),1,5) AS xAxis\n" +
        "FROM master..spt_values s\n" +
        "WHERE s.type='p'\n" +
        "AND DATEDIFF(mi,DATEADD(mi,number,'08:00'),'18:00')%60=0\n" +
        "AND number<=620) s2 on SUBSTRING(CONVERT(varchar(100), TKT_TIME, 8),1,2)+':00' = s2.xAxis\n" +
        "group by xAxis")
    List<PersonChartVo> personAnalysis();

//    @DS("master")
    @Select("SELECT count(qc.task_center_type) as value,sd.dict_label as name\n" +
        "FROM qsc_tax_task_center_info qc\n" +
        "JOIN (select sdd.dict_label,sdd.dict_value from sys_dict_data sdd where sdd.dict_type='dm_task_center_type') sd\n" +
        "ON qc.task_center_type = sd.dict_value\n" +
        "WHERE qc.task_center_type IN (\n" +
        "SELECT qc.task_center_type\n" +
        "FROM qsc_tax_task_center_info\n" +
        "GROUP BY qc.task_center_type\n" +
        "HAVING COUNT(*) > 0\n" +
        ")\n" +
        "GROUP BY qc.task_center_type")
    List<Map<String, Object>> queryPieTaskData();
}
