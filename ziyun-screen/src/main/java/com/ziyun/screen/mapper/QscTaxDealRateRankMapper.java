package com.ziyun.screen.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ziyun.common.annotation.DataColumn;
import com.ziyun.common.annotation.DataPermission;
import com.ziyun.screen.domain.QscTaxDealRateRank;
import com.ziyun.screen.domain.QscTaxOlDeal;
import com.ziyun.screen.domain.vo.QscTaxDealRateRankVo;
import com.ziyun.common.core.mapper.BaseMapperPlus;
import com.ziyun.screen.domain.vo.QscTaxOlDealVo;
import org.apache.ibatis.annotations.Param;

/**
 * 办税率排名Mapper接口
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
public interface QscTaxDealRateRankMapper extends BaseMapperPlus<QscTaxDealRateRankMapper, QscTaxDealRateRank, QscTaxDealRateRankVo> {
    @DataPermission({
        @DataColumn(key = "deptName", value = "dept_id"),
        @DataColumn(key = "userName", value = "user_id")
    })
    Page<QscTaxDealRateRankVo> customPageList(@Param("page") Page<QscTaxDealRateRank> page, @Param("ew") Wrapper<QscTaxDealRateRank> wrapper);
}
