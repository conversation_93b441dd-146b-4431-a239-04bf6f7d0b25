package com.ziyun.screen.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ziyun.common.annotation.DataColumn;
import com.ziyun.common.annotation.DataPermission;
import com.ziyun.screen.domain.QscTaxDiyDeal;
import com.ziyun.screen.domain.QscTaxHallOverview;
import com.ziyun.screen.domain.vo.QscTaxDiyDealVo;
import com.ziyun.common.core.mapper.BaseMapperPlus;
import com.ziyun.screen.domain.vo.QscTaxHallOverviewVo;
import org.apache.ibatis.annotations.Param;

/**
 * 自助办Mapper接口
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
public interface QscTaxDiyDealMapper extends BaseMapperPlus<QscTaxDiyDealMapper, QscTaxDiyDeal, QscTaxDiyDealVo> {
    @DataPermission({
        @DataColumn(key = "deptName", value = "dept_id"),
        @DataColumn(key = "userName", value = "user_id")
    })
    Page<QscTaxDiyDealVo> customPageList(@Param("page") Page<QscTaxDiyDeal> page, @Param("ew") Wrapper<QscTaxDiyDeal> wrapper);
}
