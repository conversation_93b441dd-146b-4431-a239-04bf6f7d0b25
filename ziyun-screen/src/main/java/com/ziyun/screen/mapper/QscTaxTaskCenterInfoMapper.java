package com.ziyun.screen.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ziyun.common.annotation.DataColumn;
import com.ziyun.common.annotation.DataPermission;
import com.ziyun.screen.domain.QscTaxIncomeYear;
import com.ziyun.screen.domain.QscTaxTaskCenterInfo;
import com.ziyun.screen.domain.vo.QscTaxIncomeYearVo;
import com.ziyun.screen.domain.vo.QscTaxTaskCenterInfoVo;
import com.ziyun.common.core.mapper.BaseMapperPlus;
import com.ziyun.screen.domain.vo.TaxTaskCenterInfoImportVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 斗门任务中心信息Mapper接口
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
public interface QscTaxTaskCenterInfoMapper extends BaseMapperPlus<QscTaxTaskCenterInfoMapper, QscTaxTaskCenterInfo, QscTaxTaskCenterInfoVo> {

    @Select("SELECT date, done, doing\n" +
        "FROM\n" +
        "  (SELECT a.date,\n" +
        "          COALESCE(sum(IF(b.task_status like '1', 1, 0)), 0) AS done,\n" +
        "          COALESCE(sum(IF(b.task_status like '0', 1, 0)), 0) AS doing\n" +
        "   FROM (SELECT '01' AS date UNION\n" +
        "         SELECT '02' AS date UNION\n" +
        "         SELECT '03' AS date UNION\n" +
        "         SELECT '04' AS date UNION\n" +
        "         SELECT '05' AS date UNION\n" +
        "         SELECT '06' AS date UNION\n" +
        "         SELECT '07' AS date UNION\n" +
        "         SELECT '08' AS date UNION\n" +
        "         SELECT '09' AS date UNION\n" +
        "         SELECT '10' AS date UNION\n" +
        "         SELECT '11' AS date UNION\n" +
        "         SELECT '12' AS date) a\n" +
        "   LEFT JOIN qsc_tax_task_center_info b\n" +
        "   ON a.date = DATE_FORMAT(b.task_create_time, '%m')\n" +
        "   AND b.task_center_type = #{taskCenterType}\n" +
        "   GROUP BY a.date\n" +
        "   UNION\n" +
        "   SELECT DATE_FORMAT(b.task_create_time, '%m') AS date,\n" +
        "          sum(IF(b.task_status like '1', 1, 0)) AS done,\n" +
        "          sum(IF(b.task_status like '0', 1, 0)) AS doing\n" +
        "   FROM qsc_tax_task_center_info b\n" +
        "   WHERE b.task_center_type = #{taskCenterType}) x\n" +
        "GROUP BY date\n" +
        "ORDER BY date;")
    List<Map<String,Object>> queryTaskBar(String taskCenterType);

    @DataPermission({
        @DataColumn(key = "deptName", value = "dept_id"),
        @DataColumn(key = "userName", value = "user_id")
    })
    Page<QscTaxTaskCenterInfoVo> customPageList(@Param("page") Page<QscTaxTaskCenterInfo> page, @Param("ew") Wrapper<QscTaxTaskCenterInfo> wrapper);

}
