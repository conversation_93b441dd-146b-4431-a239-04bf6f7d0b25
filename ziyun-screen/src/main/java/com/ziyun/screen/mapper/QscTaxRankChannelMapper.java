package com.ziyun.screen.mapper;

import com.ziyun.screen.domain.QscTaxRankChannel;
import com.ziyun.screen.domain.vo.QscTaxRankChannelVo;
import com.ziyun.common.core.mapper.BaseMapperPlus;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 好差评数据渠道Mapper接口
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
public interface QscTaxRankChannelMapper extends BaseMapperPlus<QscTaxRankChannelMapper, QscTaxRankChannel, QscTaxRankChannelVo> {

    @Select("select id, qtrc.rank_vol, sdd.dict_label rank_channel_name, rank_rate\n" +
        "from qsc_tax_rank_channel qtrc left join sys_dict_data sdd on qtrc.rank_channel_name = sdd.dict_value\n" +
        "where sdd.dict_type = 'tax_rank_channel'")
    List<QscTaxRankChannelVo> rankChannelVos();
}
