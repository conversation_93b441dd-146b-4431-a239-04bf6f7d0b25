package com.ziyun.screen.service;

import com.ziyun.screen.domain.QscTaxWinDeal;
import com.ziyun.screen.domain.vo.QscTaxWinDealVo;
import com.ziyun.screen.domain.bo.QscTaxWinDealBo;
import com.ziyun.common.core.page.TableDataInfo;
import com.ziyun.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 窗口办Service接口
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
public interface IQscTaxWinDealService {

    /**
     * 查询窗口办
     */
    QscTaxWinDealVo queryById(Long id);

    /**
     * 查询窗口办列表
     */
    TableDataInfo<QscTaxWinDealVo> queryPageList(QscTaxWinDealBo bo, PageQuery pageQuery);

    /**
     * 查询窗口办列表
     */
    List<QscTaxWinDealVo> queryList(QscTaxWinDealBo bo);

    /**
     * 新增窗口办
     */
    Boolean insertByBo(QscTaxWinDealBo bo);

    /**
     * 修改窗口办
     */
    Boolean updateByBo(QscTaxWinDealBo bo);

    /**
     * 校验并批量删除窗口办信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
