package com.ziyun.screen.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.ziyun.common.utils.StringUtils;
import com.ziyun.common.core.page.TableDataInfo;
import com.ziyun.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ziyun.screen.domain.bo.QscPhoneAnalysisBo;
import com.ziyun.screen.domain.vo.QscPhoneAnalysisVo;
import com.ziyun.screen.domain.QscPhoneAnalysis;
import com.ziyun.screen.mapper.QscPhoneAnalysisMapper;
import com.ziyun.screen.service.IQscPhoneAnalysisService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 来电情况分析Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
@RequiredArgsConstructor
@Service
@DS("master")
public class QscPhoneAnalysisServiceImpl implements IQscPhoneAnalysisService {

    private final QscPhoneAnalysisMapper baseMapper;

    /**
     * 查询来电情况分析
     */
    @Override
    public QscPhoneAnalysisVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询来电情况分析列表
     */
    @Override
    public TableDataInfo<QscPhoneAnalysisVo> queryPageList(QscPhoneAnalysisBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<QscPhoneAnalysis> lqw = buildQueryWrapper(bo);
        Page<QscPhoneAnalysisVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询来电情况分析列表
     */
    @Override
    public List<QscPhoneAnalysisVo> queryList(QscPhoneAnalysisBo bo) {
        LambdaQueryWrapper<QscPhoneAnalysis> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<QscPhoneAnalysis> buildQueryWrapper(QscPhoneAnalysisBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<QscPhoneAnalysis> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getCallTimeAvg() != null, QscPhoneAnalysis::getCallTimeAvg, bo.getCallTimeAvg());
        lqw.eq(bo.getWaitingCallinCount() != null, QscPhoneAnalysis::getWaitingCallinCount, bo.getWaitingCallinCount());
        lqw.eq(bo.getWaitingCallinTimeAvg() != null, QscPhoneAnalysis::getWaitingCallinTimeAvg, bo.getWaitingCallinTimeAvg());
        lqw.eq(bo.getUserId() != null, QscPhoneAnalysis::getUserId, bo.getUserId());
        lqw.eq(bo.getDeptId() != null, QscPhoneAnalysis::getDeptId, bo.getDeptId());
        return lqw;
    }

    /**
     * 新增来电情况分析
     */
    @Override
    public Boolean insertByBo(QscPhoneAnalysisBo bo) {
        QscPhoneAnalysis add = BeanUtil.toBean(bo, QscPhoneAnalysis.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改来电情况分析
     */
    @Override
    public Boolean updateByBo(QscPhoneAnalysisBo bo) {
        QscPhoneAnalysis update = BeanUtil.toBean(bo, QscPhoneAnalysis.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(QscPhoneAnalysis entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除来电情况分析
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
