package com.ziyun.screen.service;

import com.ziyun.screen.domain.QscTaxStockInOut;
import com.ziyun.screen.domain.vo.QscTaxStockInOutVo;
import com.ziyun.screen.domain.bo.QscTaxStockInOutBo;
import com.ziyun.common.core.page.TableDataInfo;
import com.ziyun.common.core.domain.PageQuery;
import com.ziyun.screen.domain.vo.halldata.RegionStockInOutVo;

import java.util.Collection;
import java.util.List;

/**
 * 入库出库Service接口
 *
 * <AUTHOR>
 * @date 2022-12-08
 */
public interface IQscTaxStockInOutService {

    /**
     * 查询入库出库
     */
    QscTaxStockInOutVo queryById(Long stockId);

    /**
     * 查询入库出库列表
     */
    TableDataInfo<QscTaxStockInOutVo> queryPageList(QscTaxStockInOutBo bo, PageQuery pageQuery);

    /**
     * 查询入库出库列表
     */
    List<QscTaxStockInOutVo> queryList(QscTaxStockInOutBo bo);

    /**
     * 新增入库出库
     */
    Boolean insertByBo(QscTaxStockInOutBo bo);

    /**
     * 修改入库出库
     */
    Boolean updateByBo(QscTaxStockInOutBo bo);

    /**
     * 校验并批量删除入库出库信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);


    QscTaxStockInOut selectStockByTaxPayerName(String taxPayerName);

    int insertStockInOut(QscTaxStockInOut inOut);

    int updateStockInOut(QscTaxStockInOut inOut);

    List<RegionStockInOutVo> queryListByRegionId(String regionPid);
}
