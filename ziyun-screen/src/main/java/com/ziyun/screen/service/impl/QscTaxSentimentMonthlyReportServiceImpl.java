package com.ziyun.screen.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.ziyun.common.utils.StringUtils;
import com.ziyun.common.core.page.TableDataInfo;
import com.ziyun.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ziyun.screen.domain.bo.QscTaxSentimentMonthlyReportBo;
import com.ziyun.screen.domain.vo.QscTaxSentimentMonthlyReportVo;
import com.ziyun.screen.domain.QscTaxSentimentMonthlyReport;
import com.ziyun.screen.mapper.QscTaxSentimentMonthlyReportMapper;
import com.ziyun.screen.service.IQscTaxSentimentMonthlyReportService;
import com.ziyun.system.service.ISysOssService;
import java.util.List;
import java.util.Map;
import java.util.Collection;
import java.util.ArrayList;
import java.util.Arrays;

/**
 * 税情感知月报Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-25
 */
@RequiredArgsConstructor
@Service
@DS("master")
public class QscTaxSentimentMonthlyReportServiceImpl implements IQscTaxSentimentMonthlyReportService {

    private final QscTaxSentimentMonthlyReportMapper baseMapper;
    private final ISysOssService ossService;

    /**
     * 查询税情感知月报
     */
    @Override
    public QscTaxSentimentMonthlyReportVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询税情感知月报列表
     */
    @Override
    public TableDataInfo<QscTaxSentimentMonthlyReportVo> queryPageList(QscTaxSentimentMonthlyReportBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<QscTaxSentimentMonthlyReport> lqw = buildQueryWrapper(bo);
        Page<QscTaxSentimentMonthlyReportVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询税情感知月报列表
     */
    @Override
    public List<QscTaxSentimentMonthlyReportVo> queryList(QscTaxSentimentMonthlyReportBo bo) {
        LambdaQueryWrapper<QscTaxSentimentMonthlyReport> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<QscTaxSentimentMonthlyReport> buildQueryWrapper(QscTaxSentimentMonthlyReportBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<QscTaxSentimentMonthlyReport> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getReportDate() != null, QscTaxSentimentMonthlyReport::getReportDate, bo.getReportDate());
        lqw.eq(StringUtils.isNotBlank(bo.getImageUrls()), QscTaxSentimentMonthlyReport::getImageUrls, bo.getImageUrls());
        lqw.eq(StringUtils.isNotBlank(bo.getTextContent()), QscTaxSentimentMonthlyReport::getTextContent, bo.getTextContent());
        lqw.eq(StringUtils.isNotBlank(bo.getRemarks()), QscTaxSentimentMonthlyReport::getRemarks, bo.getRemarks());
        lqw.eq(bo.getUserId() != null, QscTaxSentimentMonthlyReport::getUserId, bo.getUserId());
        lqw.eq(bo.getDeptId() != null, QscTaxSentimentMonthlyReport::getDeptId, bo.getDeptId());
        return lqw;
    }

    /**
     * 新增税情感知月报
     */
    @Override
    public Boolean insertByBo(QscTaxSentimentMonthlyReportBo bo) {
        QscTaxSentimentMonthlyReport add = BeanUtil.toBean(bo, QscTaxSentimentMonthlyReport.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改税情感知月报
     */
    @Override
    public Boolean updateByBo(QscTaxSentimentMonthlyReportBo bo) {
        QscTaxSentimentMonthlyReport update = BeanUtil.toBean(bo, QscTaxSentimentMonthlyReport.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(QscTaxSentimentMonthlyReport entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除税情感知月报
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            // 获取所有要删除的记录
            List<QscTaxSentimentMonthlyReportVo> reports = baseMapper.selectVoList(
                Wrappers.<QscTaxSentimentMonthlyReport>lambdaQuery()
                    .in(QscTaxSentimentMonthlyReport::getId, ids)
            );

            // 收集所有的OSS文件ID
            List<Long> ossIds = new ArrayList<>();
            for (QscTaxSentimentMonthlyReportVo report : reports) {
                if (StringUtils.isNotEmpty(report.getImageUrls())) {
                    String[] imageIds = report.getImageUrls().split(",");
                    for (String imageId : imageIds) {
                        try {
                            ossIds.add(Long.parseLong(imageId));
                        } catch (NumberFormatException e) {
                            // 忽略无效的ID
                        }
                    }
                }
            }

            // 删除OSS文件
            if (!ossIds.isEmpty()) {
                ossService.deleteWithValidByIds(ossIds, true);
            }
        }

        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
