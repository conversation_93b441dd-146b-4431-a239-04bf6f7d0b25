package com.ziyun.screen.service;

import com.ziyun.screen.domain.QscTaxBizVol;
import com.ziyun.screen.domain.vo.QscTaxBizVolVo;
import com.ziyun.screen.domain.bo.QscTaxBizVolBo;
import com.ziyun.common.core.page.TableDataInfo;
import com.ziyun.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 业务量总览Service接口
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
public interface IQscTaxBizVolService {

    /**
     * 查询业务量总览
     */
    QscTaxBizVolVo queryById(Long id);

    /**
     * 查询业务量总览列表
     */
    TableDataInfo<QscTaxBizVolVo> queryPageList(QscTaxBizVolBo bo, PageQuery pageQuery);

    /**
     * 查询业务量总览列表
     */
    List<QscTaxBizVolVo> queryList(QscTaxBizVolBo bo);

    /**
     * 新增业务量总览
     */
    Boolean insertByBo(QscTaxBizVolBo bo);

    /**
     * 修改业务量总览
     */
    Boolean updateByBo(QscTaxBizVolBo bo);

    /**
     * 校验并批量删除业务量总览信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
