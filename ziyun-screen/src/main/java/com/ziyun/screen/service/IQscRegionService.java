package com.ziyun.screen.service;

import com.ziyun.screen.domain.QscRegion;
import com.ziyun.screen.domain.vo.QscRegionVo;
import com.ziyun.screen.domain.bo.QscRegionBo;
import com.ziyun.common.core.page.TableDataInfo;
import com.ziyun.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 地区Service接口
 *
 * <AUTHOR>
 * @date 2022-11-08
 */
public interface IQscRegionService {

    /**
     * 查询地区
     */
    QscRegionVo queryById(Long id);

    /**
     * 查询地区列表
     */
    TableDataInfo<QscRegionVo> queryPageList(QscRegionBo bo, PageQuery pageQuery);

    /**
     * 查询地区列表
     */
    List<QscRegionVo> queryList(QscRegionBo bo);

    /**
     * 新增地区
     */
    Boolean insertByBo(QscRegionBo bo);

    /**
     * 修改地区
     */
    Boolean updateByBo(QscRegionBo bo);

    /**
     * 校验并批量删除地区信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    QscRegion selectRegionByExtName(String name);
}
