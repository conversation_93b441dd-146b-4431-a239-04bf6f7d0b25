package com.ziyun.screen.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.ziyun.common.utils.StringUtils;
import com.ziyun.common.core.page.TableDataInfo;
import com.ziyun.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ziyun.screen.domain.bo.QscTaxDiyDealBo;
import com.ziyun.screen.domain.vo.QscTaxDiyDealVo;
import com.ziyun.screen.domain.QscTaxDiyDeal;
import com.ziyun.screen.mapper.QscTaxDiyDealMapper;
import com.ziyun.screen.service.IQscTaxDiyDealService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 自助办Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
@RequiredArgsConstructor
@Service
@DS("master")
public class QscTaxDiyDealServiceImpl implements IQscTaxDiyDealService {

    private final QscTaxDiyDealMapper baseMapper;

    /**
     * 查询自助办
     */
    @Override
    public QscTaxDiyDealVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询自助办列表
     */
    @Override
    public TableDataInfo<QscTaxDiyDealVo> queryPageList(QscTaxDiyDealBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<QscTaxDiyDeal> lqw = buildQueryWrapper(bo);
        Page<QscTaxDiyDealVo> result = baseMapper.customPageList(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询自助办列表
     */
    @Override
    public List<QscTaxDiyDealVo> queryList(QscTaxDiyDealBo bo) {
        LambdaQueryWrapper<QscTaxDiyDeal> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<QscTaxDiyDeal> buildQueryWrapper(QscTaxDiyDealBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<QscTaxDiyDeal> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getName()), QscTaxDiyDeal::getName, bo.getName());
        lqw.eq(bo.getValue() != null, QscTaxDiyDeal::getValue, bo.getValue());
        lqw.eq(bo.getUserId() != null, QscTaxDiyDeal::getUserId, bo.getUserId());
        lqw.eq(bo.getDeptId() != null, QscTaxDiyDeal::getDeptId, bo.getDeptId());
        return lqw;
    }

    /**
     * 新增自助办
     */
    @Override
    public Boolean insertByBo(QscTaxDiyDealBo bo) {
        QscTaxDiyDeal add = BeanUtil.toBean(bo, QscTaxDiyDeal.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改自助办
     */
    @Override
    public Boolean updateByBo(QscTaxDiyDealBo bo) {
        QscTaxDiyDeal update = BeanUtil.toBean(bo, QscTaxDiyDeal.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(QscTaxDiyDeal entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除自助办
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
