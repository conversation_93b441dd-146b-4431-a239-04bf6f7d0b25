package com.ziyun.screen.service;

import com.ziyun.screen.domain.QscTaxCostDealTag;
import com.ziyun.screen.domain.vo.QscTaxCostDealTagVo;
import com.ziyun.screen.domain.bo.QscTaxCostDealTagBo;
import com.ziyun.common.core.page.TableDataInfo;
import com.ziyun.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 税费协同工单标签Service接口
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
public interface IQscTaxCostDealTagService {

    /**
     * 查询税费协同工单标签
     */
    QscTaxCostDealTagVo queryById(Long id);

    /**
     * 查询税费协同工单标签列表
     */
    TableDataInfo<QscTaxCostDealTagVo> queryPageList(QscTaxCostDealTagBo bo, PageQuery pageQuery);

    /**
     * 查询税费协同工单标签列表
     */
    List<QscTaxCostDealTagVo> queryList(QscTaxCostDealTagBo bo);

    /**
     * 新增税费协同工单标签
     */
    Boolean insertByBo(QscTaxCostDealTagBo bo);

    /**
     * 修改税费协同工单标签
     */
    Boolean updateByBo(QscTaxCostDealTagBo bo);

    /**
     * 校验并批量删除税费协同工单标签信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
