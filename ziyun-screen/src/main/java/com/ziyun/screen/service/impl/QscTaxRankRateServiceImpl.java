package com.ziyun.screen.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.ziyun.common.utils.StringUtils;
import com.ziyun.common.core.page.TableDataInfo;
import com.ziyun.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ziyun.screen.domain.bo.QscTaxRankRateBo;
import com.ziyun.screen.domain.vo.QscTaxRankRateVo;
import com.ziyun.screen.domain.QscTaxRankRate;
import com.ziyun.screen.mapper.QscTaxRankRateMapper;
import com.ziyun.screen.service.IQscTaxRankRateService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 好差评数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
@RequiredArgsConstructor
@Service
@DS("master")
public class QscTaxRankRateServiceImpl implements IQscTaxRankRateService {

    private final QscTaxRankRateMapper baseMapper;

    /**
     * 查询好差评数据
     */
    @Override
    public QscTaxRankRateVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询好差评数据列表
     */
    @Override
    public TableDataInfo<QscTaxRankRateVo> queryPageList(QscTaxRankRateBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<QscTaxRankRate> lqw = buildQueryWrapper(bo);
        Page<QscTaxRankRateVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询好差评数据列表
     */
    @Override
    public List<QscTaxRankRateVo> queryList(QscTaxRankRateBo bo) {
        LambdaQueryWrapper<QscTaxRankRate> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<QscTaxRankRate> buildQueryWrapper(QscTaxRankRateBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<QscTaxRankRate> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getBestRank() != null, QscTaxRankRate::getBestRank, bo.getBestRank());
        lqw.eq(bo.getBadRank() != null, QscTaxRankRate::getBadRank, bo.getBadRank());
        lqw.eq(bo.getUserId() != null, QscTaxRankRate::getUserId, bo.getUserId());
        lqw.eq(bo.getDeptId() != null, QscTaxRankRate::getDeptId, bo.getDeptId());
        return lqw;
    }

    /**
     * 新增好差评数据
     */
    @Override
    public Boolean insertByBo(QscTaxRankRateBo bo) {
        QscTaxRankRate add = BeanUtil.toBean(bo, QscTaxRankRate.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改好差评数据
     */
    @Override
    public Boolean updateByBo(QscTaxRankRateBo bo) {
        QscTaxRankRate update = BeanUtil.toBean(bo, QscTaxRankRate.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(QscTaxRankRate entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除好差评数据
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
