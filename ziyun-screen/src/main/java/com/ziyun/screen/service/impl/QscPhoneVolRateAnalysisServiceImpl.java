package com.ziyun.screen.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.ziyun.common.utils.StringUtils;
import com.ziyun.common.core.page.TableDataInfo;
import com.ziyun.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ziyun.screen.domain.bo.QscPhoneVolRateAnalysisBo;
import com.ziyun.screen.domain.vo.QscPhoneVolRateAnalysisVo;
import com.ziyun.screen.domain.QscPhoneVolRateAnalysis;
import com.ziyun.screen.mapper.QscPhoneVolRateAnalysisMapper;
import com.ziyun.screen.service.IQscPhoneVolRateAnalysisService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 月电话接听量和接听率Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
@RequiredArgsConstructor
@Service
@DS("master")
public class QscPhoneVolRateAnalysisServiceImpl implements IQscPhoneVolRateAnalysisService {

    private final QscPhoneVolRateAnalysisMapper baseMapper;

    /**
     * 查询月电话接听量和接听率
     */
    @Override
    public QscPhoneVolRateAnalysisVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询月电话接听量和接听率列表
     */
    @Override
    public TableDataInfo<QscPhoneVolRateAnalysisVo> queryPageList(QscPhoneVolRateAnalysisBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<QscPhoneVolRateAnalysis> lqw = buildQueryWrapper(bo);
        Page<QscPhoneVolRateAnalysisVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询月电话接听量和接听率列表
     */
    @Override
    public List<QscPhoneVolRateAnalysisVo> queryList(QscPhoneVolRateAnalysisBo bo) {
        LambdaQueryWrapper<QscPhoneVolRateAnalysis> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<QscPhoneVolRateAnalysis> buildQueryWrapper(QscPhoneVolRateAnalysisBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<QscPhoneVolRateAnalysis> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getAxisX() != null, QscPhoneVolRateAnalysis::getAxisX, bo.getAxisX());
        lqw.eq(bo.getBarY() != null, QscPhoneVolRateAnalysis::getBarY, bo.getBarY());
        lqw.eq(bo.getLineY() != null, QscPhoneVolRateAnalysis::getLineY, bo.getLineY());
        lqw.eq(bo.getUserId() != null, QscPhoneVolRateAnalysis::getUserId, bo.getUserId());
        lqw.eq(bo.getDeptId() != null, QscPhoneVolRateAnalysis::getDeptId, bo.getDeptId());
        return lqw;
    }

    /**
     * 新增月电话接听量和接听率
     */
    @Override
    public Boolean insertByBo(QscPhoneVolRateAnalysisBo bo) {
        QscPhoneVolRateAnalysis add = BeanUtil.toBean(bo, QscPhoneVolRateAnalysis.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改月电话接听量和接听率
     */
    @Override
    public Boolean updateByBo(QscPhoneVolRateAnalysisBo bo) {
        QscPhoneVolRateAnalysis update = BeanUtil.toBean(bo, QscPhoneVolRateAnalysis.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(QscPhoneVolRateAnalysis entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除月电话接听量和接听率
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
