package com.ziyun.screen.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.ziyun.common.utils.StringUtils;
import com.ziyun.common.core.page.TableDataInfo;
import com.ziyun.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ziyun.screen.domain.bo.QscPhoneTagAnalysisBo;
import com.ziyun.screen.domain.vo.QscPhoneTagAnalysisVo;
import com.ziyun.screen.domain.QscPhoneTagAnalysis;
import com.ziyun.screen.mapper.QscPhoneTagAnalysisMapper;
import com.ziyun.screen.service.IQscPhoneTagAnalysisService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 电话热点咨询Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
@RequiredArgsConstructor
@Service
@DS("master")
public class QscPhoneTagAnalysisServiceImpl implements IQscPhoneTagAnalysisService {

    private final QscPhoneTagAnalysisMapper baseMapper;

    /**
     * 查询电话热点咨询
     */
    @Override
    public QscPhoneTagAnalysisVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询电话热点咨询列表
     */
    @Override
    public TableDataInfo<QscPhoneTagAnalysisVo> queryPageList(QscPhoneTagAnalysisBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<QscPhoneTagAnalysis> lqw = buildQueryWrapper(bo);
        Page<QscPhoneTagAnalysisVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询电话热点咨询列表
     */
    @Override
    public List<QscPhoneTagAnalysisVo> queryList(QscPhoneTagAnalysisBo bo) {
        LambdaQueryWrapper<QscPhoneTagAnalysis> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<QscPhoneTagAnalysis> buildQueryWrapper(QscPhoneTagAnalysisBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<QscPhoneTagAnalysis> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getAnaylsisTime() != null, QscPhoneTagAnalysis::getAnaylsisTime, bo.getAnaylsisTime());
        lqw.like(StringUtils.isNotBlank(bo.getHotName()), QscPhoneTagAnalysis::getHotName, bo.getHotName());
        lqw.eq(bo.getHotVol() != null, QscPhoneTagAnalysis::getHotVol, bo.getHotVol());
        lqw.eq(bo.getHotRate() != null, QscPhoneTagAnalysis::getHotRate, bo.getHotRate());
        lqw.eq(bo.getUserId() != null, QscPhoneTagAnalysis::getUserId, bo.getUserId());
        lqw.eq(bo.getDeptId() != null, QscPhoneTagAnalysis::getDeptId, bo.getDeptId());
        return lqw;
    }

    /**
     * 新增电话热点咨询
     */
    @Override
    public Boolean insertByBo(QscPhoneTagAnalysisBo bo) {
        QscPhoneTagAnalysis add = BeanUtil.toBean(bo, QscPhoneTagAnalysis.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改电话热点咨询
     */
    @Override
    public Boolean updateByBo(QscPhoneTagAnalysisBo bo) {
        QscPhoneTagAnalysis update = BeanUtil.toBean(bo, QscPhoneTagAnalysis.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(QscPhoneTagAnalysis entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除电话热点咨询
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
