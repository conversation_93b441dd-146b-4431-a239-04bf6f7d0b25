package com.ziyun.screen.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.ziyun.common.utils.StringUtils;
import com.ziyun.common.core.page.TableDataInfo;
import com.ziyun.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ziyun.screen.domain.bo.QscRegionBo;
import com.ziyun.screen.domain.vo.QscRegionVo;
import com.ziyun.screen.domain.QscRegion;
import com.ziyun.screen.mapper.QscRegionMapper;
import com.ziyun.screen.service.IQscRegionService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 地区Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-11-08
 */
@RequiredArgsConstructor
@Service
@DS("master")
public class QscRegionServiceImpl implements IQscRegionService {

    private final QscRegionMapper baseMapper;

    /**
     * 查询地区
     */
    @Override
    public QscRegionVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询地区列表
     */
    @Override
    public TableDataInfo<QscRegionVo> queryPageList(QscRegionBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<QscRegion> lqw = buildQueryWrapper(bo);
        Page<QscRegionVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询地区列表
     */
    @Override
    public List<QscRegionVo> queryList(QscRegionBo bo) {
        LambdaQueryWrapper<QscRegion> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<QscRegion> buildQueryWrapper(QscRegionBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<QscRegion> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getPid() != null, QscRegion::getPid, bo.getPid());
        lqw.eq(bo.getDeep() != null, QscRegion::getDeep, bo.getDeep());
        lqw.like(StringUtils.isNotBlank(bo.getName()), QscRegion::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getPinyinPrefix()), QscRegion::getPinyinPrefix, bo.getPinyinPrefix());
        lqw.eq(StringUtils.isNotBlank(bo.getPinyin()), QscRegion::getPinyin, bo.getPinyin());
        lqw.eq(bo.getExtId() != null, QscRegion::getExtId, bo.getExtId());
        lqw.like(StringUtils.isNotBlank(bo.getExtName()), QscRegion::getExtName, bo.getExtName());
        return lqw;
    }

    /**
     * 新增地区
     */
    @Override
    public Boolean insertByBo(QscRegionBo bo) {
        QscRegion add = BeanUtil.toBean(bo, QscRegion.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改地区
     */
    @Override
    public Boolean updateByBo(QscRegionBo bo) {
        QscRegion update = BeanUtil.toBean(bo, QscRegion.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(QscRegion entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除地区
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public QscRegion selectRegionByExtName(String name) {
        return baseMapper.selectRegionByExtName(name);
    }
}
