package com.ziyun.screen.service;

import com.ziyun.screen.domain.QscTaxCloudDeal;
import com.ziyun.screen.domain.vo.QscTaxCloudDealVo;
import com.ziyun.screen.domain.bo.QscTaxCloudDealBo;
import com.ziyun.common.core.page.TableDataInfo;
import com.ziyun.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 云厅办Service接口
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
public interface IQscTaxCloudDealService {

    /**
     * 查询云厅办
     */
    QscTaxCloudDealVo queryById(Long id);

    /**
     * 查询云厅办列表
     */
    TableDataInfo<QscTaxCloudDealVo> queryPageList(QscTaxCloudDealBo bo, PageQuery pageQuery);

    /**
     * 查询云厅办列表
     */
    List<QscTaxCloudDealVo> queryList(QscTaxCloudDealBo bo);

    /**
     * 新增云厅办
     */
    Boolean insertByBo(QscTaxCloudDealBo bo);

    /**
     * 修改云厅办
     */
    Boolean updateByBo(QscTaxCloudDealBo bo);

    /**
     * 校验并批量删除云厅办信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
