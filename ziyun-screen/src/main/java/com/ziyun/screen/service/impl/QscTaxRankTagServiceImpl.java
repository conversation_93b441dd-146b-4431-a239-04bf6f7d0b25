package com.ziyun.screen.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.ziyun.common.utils.StringUtils;
import com.ziyun.common.core.page.TableDataInfo;
import com.ziyun.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ziyun.screen.domain.chart.TagV;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ziyun.screen.domain.bo.QscTaxRankTagBo;
import com.ziyun.screen.domain.vo.QscTaxRankTagVo;
import com.ziyun.screen.domain.QscTaxRankTag;
import com.ziyun.screen.mapper.QscTaxRankTagMapper;
import com.ziyun.screen.service.IQscTaxRankTagService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 好差评标签Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
@RequiredArgsConstructor
@Service
@DS("master")
public class QscTaxRankTagServiceImpl implements IQscTaxRankTagService {

    private final QscTaxRankTagMapper baseMapper;

    /**
     * 查询好差评标签
     */
    @Override
    public QscTaxRankTagVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询好差评标签列表
     */
    @Override
    public TableDataInfo<QscTaxRankTagVo> queryPageList(QscTaxRankTagBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<QscTaxRankTag> lqw = buildQueryWrapper(bo);
        Page<QscTaxRankTagVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询好差评标签列表
     */
    @Override
    public List<QscTaxRankTagVo> queryList(QscTaxRankTagBo bo) {
        LambdaQueryWrapper<QscTaxRankTag> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<QscTaxRankTag> buildQueryWrapper(QscTaxRankTagBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<QscTaxRankTag> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getTagName()), QscTaxRankTag::getTagName, bo.getTagName());
        lqw.eq(bo.getTaxHallOne() != null, QscTaxRankTag::getTaxHallOne, bo.getTaxHallOne());
        lqw.eq(bo.getTaxHallTwo() != null, QscTaxRankTag::getTaxHallTwo, bo.getTaxHallTwo());
        lqw.eq(bo.getTaxHallThr() != null, QscTaxRankTag::getTaxHallThr, bo.getTaxHallThr());
        lqw.eq(bo.getBizPer() != null, QscTaxRankTag::getBizPer, bo.getBizPer());
        lqw.eq(bo.getUserId() != null, QscTaxRankTag::getUserId, bo.getUserId());
        lqw.eq(bo.getDeptId() != null, QscTaxRankTag::getDeptId, bo.getDeptId());
        return lqw;
    }

    /**
     * 新增好差评标签
     */
    @Override
    public Boolean insertByBo(QscTaxRankTagBo bo) {
        QscTaxRankTag add = BeanUtil.toBean(bo, QscTaxRankTag.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改好差评标签
     */
    @Override
    public Boolean updateByBo(QscTaxRankTagBo bo) {
        QscTaxRankTag update = BeanUtil.toBean(bo, QscTaxRankTag.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(QscTaxRankTag entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除好差评标签
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public List<TagV> maxRankTag() {
        return baseMapper.queyMaxRankTag();
    }

    @Override
    public Integer maxRankVal() {
        List<Integer> list =  this.baseMapper.maxRankVal();
        return list.get(0);
    }
}
