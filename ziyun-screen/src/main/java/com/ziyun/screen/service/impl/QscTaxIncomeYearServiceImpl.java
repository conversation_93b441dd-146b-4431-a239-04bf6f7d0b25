package com.ziyun.screen.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.ziyun.common.utils.StringUtils;
import com.ziyun.common.core.page.TableDataInfo;
import com.ziyun.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ziyun.screen.domain.bo.QscTaxIncomeYearBo;
import com.ziyun.screen.domain.vo.QscTaxIncomeYearVo;
import com.ziyun.screen.domain.QscTaxIncomeYear;
import com.ziyun.screen.mapper.QscTaxIncomeYearMapper;
import com.ziyun.screen.service.IQscTaxIncomeYearService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 税收收入(年)Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
@RequiredArgsConstructor
@Service
@DS("master")
public class QscTaxIncomeYearServiceImpl implements IQscTaxIncomeYearService {

    private final QscTaxIncomeYearMapper baseMapper;

    /**
     * 查询税收收入(年)
     */
    @Override
    public QscTaxIncomeYearVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询税收收入(年)列表
     */
    @Override
    public TableDataInfo<QscTaxIncomeYearVo> queryPageList(QscTaxIncomeYearBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<QscTaxIncomeYear> lqw = buildQueryWrapper(bo);
        Page<QscTaxIncomeYearVo> result = baseMapper.customPageList(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询税收收入(年)列表
     */
    @Override
    public List<QscTaxIncomeYearVo> queryList(QscTaxIncomeYearBo bo) {
        LambdaQueryWrapper<QscTaxIncomeYear> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<QscTaxIncomeYear> buildQueryWrapper(QscTaxIncomeYearBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<QscTaxIncomeYear> lqw = Wrappers.lambdaQuery();
        // 对年份进行模糊匹配，格式化年份
        if (bo.getIncomeYear() != null) {
            String yearStr = String.valueOf(bo.getIncomeYear().getYear() + 1900);
            lqw.like(QscTaxIncomeYear::getIncomeYear, yearStr);
        }
        lqw.eq(bo.getIncome() != null, QscTaxIncomeYear::getIncome, bo.getIncome());
        lqw.eq(bo.getRate() != null, QscTaxIncomeYear::getRate, bo.getRate());
        lqw.eq(bo.getUserId() != null, QscTaxIncomeYear::getUserId, bo.getUserId());
        lqw.eq(bo.getDeptId() != null, QscTaxIncomeYear::getDeptId, bo.getDeptId());
        return lqw;
    }

    /**
     * 新增税收收入(年)
     */
    @Override
    public Boolean insertByBo(QscTaxIncomeYearBo bo) {
        QscTaxIncomeYear add = BeanUtil.toBean(bo, QscTaxIncomeYear.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改税收收入(年)
     */
    @Override
    public Boolean updateByBo(QscTaxIncomeYearBo bo) {
        QscTaxIncomeYear update = BeanUtil.toBean(bo, QscTaxIncomeYear.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(QscTaxIncomeYear entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除税收收入(年)
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
