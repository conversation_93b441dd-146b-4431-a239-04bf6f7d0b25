package com.ziyun.screen.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.ziyun.common.utils.StringUtils;
import com.ziyun.common.core.page.TableDataInfo;
import com.ziyun.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ziyun.screen.domain.bo.QscTaxOlDealBo;
import com.ziyun.screen.domain.vo.QscTaxOlDealVo;
import com.ziyun.screen.domain.QscTaxOlDeal;
import com.ziyun.screen.mapper.QscTaxOlDealMapper;
import com.ziyun.screen.service.IQscTaxOlDealService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 网上办Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
@RequiredArgsConstructor
@Service
@DS("master")
public class QscTaxOlDealServiceImpl implements IQscTaxOlDealService {

    private final QscTaxOlDealMapper baseMapper;

    /**
     * 查询网上办
     */
    @Override
    public QscTaxOlDealVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询网上办列表
     */
    @Override
    public TableDataInfo<QscTaxOlDealVo> queryPageList(QscTaxOlDealBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<QscTaxOlDeal> lqw = buildQueryWrapper(bo);
        Page<QscTaxOlDealVo> result = baseMapper.customPageList(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询网上办列表
     */
    @Override
    public List<QscTaxOlDealVo> queryList(QscTaxOlDealBo bo) {
        LambdaQueryWrapper<QscTaxOlDeal> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<QscTaxOlDeal> buildQueryWrapper(QscTaxOlDealBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<QscTaxOlDeal> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getName()), QscTaxOlDeal::getName, bo.getName());
        lqw.eq(bo.getValue() != null, QscTaxOlDeal::getValue, bo.getValue());
        lqw.eq(bo.getUserId() != null, QscTaxOlDeal::getUserId, bo.getUserId());
        lqw.eq(bo.getDeptId() != null, QscTaxOlDeal::getDeptId, bo.getDeptId());
        return lqw;
    }

    /**
     * 新增网上办
     */
    @Override
    public Boolean insertByBo(QscTaxOlDealBo bo) {
        QscTaxOlDeal add = BeanUtil.toBean(bo, QscTaxOlDeal.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改网上办
     */
    @Override
    public Boolean updateByBo(QscTaxOlDealBo bo) {
        QscTaxOlDeal update = BeanUtil.toBean(bo, QscTaxOlDeal.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(QscTaxOlDeal entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除网上办
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
