package com.ziyun.screen.service;

import com.ziyun.screen.domain.QscTaxTaskCenterInfo;
import com.ziyun.screen.domain.vo.QscTaxTaskCenterInfoVo;
import com.ziyun.screen.domain.bo.QscTaxTaskCenterInfoBo;
import com.ziyun.common.core.page.TableDataInfo;
import com.ziyun.common.core.domain.PageQuery;
import com.ziyun.screen.domain.vo.TaskMoudelVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.Collection;
import java.util.List;

/**
 * 斗门任务中心信息Service接口
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
public interface IQscTaxTaskCenterInfoService {

    TaskMoudelVo queryTaskMoudel(String taskCenterType);
    /**
     * 查询斗门任务中心信息
     */
    QscTaxTaskCenterInfoVo queryById(Long id);
    QscTaxTaskCenterInfo queryByOne(QscTaxTaskCenterInfoVo vo);
    /**
     * 查询斗门任务中心信息列表
     */
    TableDataInfo<QscTaxTaskCenterInfoVo> queryPageList(QscTaxTaskCenterInfoBo bo, PageQuery pageQuery);

    /**
     * 查询斗门任务中心信息列表
     */
    List<QscTaxTaskCenterInfoVo> queryList(QscTaxTaskCenterInfoBo bo);

    /**
     * 新增斗门任务中心信息
     */
    Boolean insertByBo(QscTaxTaskCenterInfoBo bo);

    /**
     * 修改斗门任务中心信息
     */
    Boolean updateByBo(QscTaxTaskCenterInfoBo bo);

    /**
     * 校验并批量删除斗门任务中心信息信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

//    queryTaskMoudel
    boolean saveOrUpdateBatch(List<QscTaxTaskCenterInfo> list);

    int insertTaskCenterInfo(QscTaxTaskCenterInfo info);

    int updateTaskCenterInfo(QscTaxTaskCenterInfo info);

    List<String> imporData(MultipartFile file, boolean updateSupport);

    TableDataInfo<QscTaxTaskCenterInfoVo> customPageList(QscTaxTaskCenterInfoBo bo, PageQuery pageQuery);
}
