package com.ziyun.screen.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.ziyun.common.utils.StringUtils;
import com.ziyun.common.core.page.TableDataInfo;
import com.ziyun.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ziyun.screen.domain.bo.QscTaxBizTopBo;
import com.ziyun.screen.domain.vo.QscTaxBizTopVo;
import com.ziyun.screen.domain.QscTaxBizTop;
import com.ziyun.screen.mapper.QscTaxBizTopMapper;
import com.ziyun.screen.service.IQscTaxBizTopService;
import com.ziyun.common.utils.DateUtils;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 月度业务量排名Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
@RequiredArgsConstructor
@Service
@DS("master")
public class QscTaxBizTopServiceImpl implements IQscTaxBizTopService {

    private final QscTaxBizTopMapper baseMapper;

    /**
     * 查询月度业务量排名
     */
    @Override
    public QscTaxBizTopVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询月度业务量排名列表
     */
    @Override
    public TableDataInfo<QscTaxBizTopVo> queryPageList(QscTaxBizTopBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<QscTaxBizTop> lqw = buildQueryWrapper(bo);
        Page<QscTaxBizTopVo> result = baseMapper.customPageList(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询月度业务量排名列表
     */
    @Override
    public List<QscTaxBizTopVo> queryList(QscTaxBizTopBo bo) {
        LambdaQueryWrapper<QscTaxBizTop> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<QscTaxBizTop> buildQueryWrapper(QscTaxBizTopBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<QscTaxBizTop> lqw = Wrappers.lambdaQuery();
        // 如果month不为空，则按年月格式进行模糊查询
        if (bo.getMonth() != null) {
            lqw.like(QscTaxBizTop::getMonth, DateUtils.formatToYearMonth(bo.getMonth()));
        }
        lqw.like(StringUtils.isNotBlank(bo.getBizName()), QscTaxBizTop::getBizName, bo.getBizName());
        lqw.eq(bo.getBizVol() != null, QscTaxBizTop::getBizVol, bo.getBizVol());
        lqw.eq(bo.getBizRatio() != null, QscTaxBizTop::getBizRatio, bo.getBizRatio());
        lqw.eq(bo.getUserId() != null, QscTaxBizTop::getUserId, bo.getUserId());
        lqw.eq(bo.getDeptId() != null, QscTaxBizTop::getDeptId, bo.getDeptId());
        return lqw;
    }

    /**
     * 新增月度业务量排名
     */
    @Override
    public Boolean insertByBo(QscTaxBizTopBo bo) {
        QscTaxBizTop add = BeanUtil.toBean(bo, QscTaxBizTop.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改月度业务量排名
     */
    @Override
    public Boolean updateByBo(QscTaxBizTopBo bo) {
        QscTaxBizTop update = BeanUtil.toBean(bo, QscTaxBizTop.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(QscTaxBizTop entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除月度业务量排名
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
