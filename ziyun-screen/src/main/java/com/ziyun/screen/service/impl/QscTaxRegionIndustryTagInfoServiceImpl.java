package com.ziyun.screen.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.ziyun.common.utils.StringUtils;
import com.ziyun.common.core.page.TableDataInfo;
import com.ziyun.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ziyun.screen.domain.bo.QscRegionBo;
import com.ziyun.screen.domain.vo.QscRegionVo;
import com.ziyun.screen.domain.vo.halldata.RegionIndustryAnaylsisVo;
import com.ziyun.screen.service.IQscRegionService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ziyun.screen.domain.bo.QscTaxRegionIndustryTagInfoBo;
import com.ziyun.screen.domain.vo.QscTaxRegionIndustryTagInfoVo;
import com.ziyun.screen.domain.QscTaxRegionIndustryTagInfo;
import com.ziyun.screen.mapper.QscTaxRegionIndustryTagInfoMapper;
import com.ziyun.screen.service.IQscTaxRegionIndustryTagInfoService;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 行业分类Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-11-08
 */
@RequiredArgsConstructor
@Service
@DS("master")
public class QscTaxRegionIndustryTagInfoServiceImpl implements IQscTaxRegionIndustryTagInfoService {

    private final IQscRegionService regionService;
    private final QscTaxRegionIndustryTagInfoMapper baseMapper;

    /**
     * 查询行业分类
     */
    @Override
    public QscTaxRegionIndustryTagInfoVo queryById(Long regionId){
        return baseMapper.selectVoById(regionId);
    }

    /**
     * 查询行业分类列表
     */
    @Override
    public TableDataInfo<QscTaxRegionIndustryTagInfoVo> queryPageList(QscTaxRegionIndustryTagInfoBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<QscTaxRegionIndustryTagInfo> lqw = buildQueryWrapper(bo);
        Page<QscTaxRegionIndustryTagInfoVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询行业分类列表
     */
    @Override
    public List<QscTaxRegionIndustryTagInfoVo> queryList(QscTaxRegionIndustryTagInfoBo bo) {
        LambdaQueryWrapper<QscTaxRegionIndustryTagInfo> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<QscTaxRegionIndustryTagInfo> buildQueryWrapper(QscTaxRegionIndustryTagInfoBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<QscTaxRegionIndustryTagInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getRegionId() != null, QscTaxRegionIndustryTagInfo::getRegionId, bo.getRegionId());
        lqw.eq(StringUtils.isNotBlank(bo.getIndustryTag()), QscTaxRegionIndustryTagInfo::getIndustryTag, bo.getIndustryTag());
        lqw.like(StringUtils.isNotBlank(bo.getIndustryTagName()), QscTaxRegionIndustryTagInfo::getIndustryTagName, bo.getIndustryTagName());
        lqw.eq(bo.getEnterpriseNum() != null, QscTaxRegionIndustryTagInfo::getEnterpriseNum, bo.getEnterpriseNum());
        lqw.eq(bo.getIndividualNum() != null, QscTaxRegionIndustryTagInfo::getIndividualNum, bo.getIndividualNum());
        lqw.eq(bo.getWriteDate() != null, QscTaxRegionIndustryTagInfo::getWriteDate, bo.getWriteDate());
        return lqw;
    }

    /**
     * 新增行业分类
     */
    @Override
    public Boolean insertByBo(QscTaxRegionIndustryTagInfoBo bo) {
        QscTaxRegionIndustryTagInfo add = BeanUtil.toBean(bo, QscTaxRegionIndustryTagInfo.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setRegionId(add.getRegionId());
        }
        return flag;
    }

    /**
     * 修改行业分类
     */
    @Override
    public Boolean updateByBo(QscTaxRegionIndustryTagInfoBo bo) {
        QscTaxRegionIndustryTagInfo update = BeanUtil.toBean(bo, QscTaxRegionIndustryTagInfo.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(QscTaxRegionIndustryTagInfo entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除行业分类
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    public List<RegionIndustryAnaylsisVo> queryListByRegionId(String id) {
        List<RegionIndustryAnaylsisVo> rsiovs = new ArrayList<>();
        if(null!=id){
            QscRegionBo bo = new QscRegionBo();
            bo.setPid(Long.valueOf(id));
            List<QscRegionVo> qscRegionVos = regionService.queryList(bo);
            for (QscRegionVo vo : qscRegionVos) {
                QscTaxRegionIndustryTagInfoBo sioBo = new QscTaxRegionIndustryTagInfoBo();
                sioBo.setRegionId(vo.getId());
                List<QscTaxRegionIndustryTagInfoVo> tagInfoVos =queryList(sioBo);
//                this.baseMapper.s
                if(0 != tagInfoVos.size()) {
                    RegionIndustryAnaylsisVo i = new RegionIndustryAnaylsisVo(vo,null,tagInfoVos);
                    rsiovs.add(i);
                }

            }
            return rsiovs;
        }
        return rsiovs;
    }
}
