package com.ziyun.screen.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.ziyun.common.utils.StringUtils;
import com.ziyun.common.core.page.TableDataInfo;
import com.ziyun.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ziyun.screen.domain.bo.QscOverviewDataBo;
import com.ziyun.screen.domain.vo.QscOverviewDataVo;
import com.ziyun.screen.domain.QscOverviewData;
import com.ziyun.screen.mapper.QscOverviewDataMapper;
import com.ziyun.screen.service.IQscOverviewDataService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 税情分析数据总览Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
@RequiredArgsConstructor
@Service
@DS("master")
public class QscOverviewDataServiceImpl implements IQscOverviewDataService {

    private final QscOverviewDataMapper baseMapper;

    /**
     * 查询税情分析数据总览
     */
    @Override
    public QscOverviewDataVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询税情分析数据总览列表
     */
    @Override
    public TableDataInfo<QscOverviewDataVo> queryPageList(QscOverviewDataBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<QscOverviewData> lqw = buildQueryWrapper(bo);
        Page<QscOverviewDataVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询税情分析数据总览列表
     */
    @Override
    public List<QscOverviewDataVo> queryList(QscOverviewDataBo bo) {
        LambdaQueryWrapper<QscOverviewData> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<QscOverviewData> buildQueryWrapper(QscOverviewDataBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<QscOverviewData> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getName()), QscOverviewData::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getCount()), QscOverviewData::getCount, bo.getCount());
        lqw.eq(StringUtils.isNotBlank(bo.getColor()), QscOverviewData::getColor, bo.getColor());
        lqw.eq(bo.getUserId() != null, QscOverviewData::getUserId, bo.getUserId());
        lqw.eq(bo.getDeptId() != null, QscOverviewData::getDeptId, bo.getDeptId());
        return lqw;
    }

    /**
     * 新增税情分析数据总览
     */
    @Override
    public Boolean insertByBo(QscOverviewDataBo bo) {
        QscOverviewData add = BeanUtil.toBean(bo, QscOverviewData.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改税情分析数据总览
     */
    @Override
    public Boolean updateByBo(QscOverviewDataBo bo) {
        QscOverviewData update = BeanUtil.toBean(bo, QscOverviewData.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(QscOverviewData entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除税情分析数据总览
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
