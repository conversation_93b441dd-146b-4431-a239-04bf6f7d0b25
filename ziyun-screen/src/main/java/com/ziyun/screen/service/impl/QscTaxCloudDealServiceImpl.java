package com.ziyun.screen.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.ziyun.common.utils.StringUtils;
import com.ziyun.common.core.page.TableDataInfo;
import com.ziyun.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ziyun.screen.domain.bo.QscTaxCloudDealBo;
import com.ziyun.screen.domain.vo.QscTaxCloudDealVo;
import com.ziyun.screen.domain.QscTaxCloudDeal;
import com.ziyun.screen.mapper.QscTaxCloudDealMapper;
import com.ziyun.screen.service.IQscTaxCloudDealService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 云厅办Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
@RequiredArgsConstructor
@Service
@DS("master")
public class QscTaxCloudDealServiceImpl implements IQscTaxCloudDealService {

    private final QscTaxCloudDealMapper baseMapper;

    /**
     * 查询云厅办
     */
    @Override
    public QscTaxCloudDealVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询云厅办列表
     */
    @Override
    public TableDataInfo<QscTaxCloudDealVo> queryPageList(QscTaxCloudDealBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<QscTaxCloudDeal> lqw = buildQueryWrapper(bo);
        Page<QscTaxCloudDealVo> result = baseMapper.customPageList(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询云厅办列表
     */
    @Override
    public List<QscTaxCloudDealVo> queryList(QscTaxCloudDealBo bo) {
        LambdaQueryWrapper<QscTaxCloudDeal> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<QscTaxCloudDeal> buildQueryWrapper(QscTaxCloudDealBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<QscTaxCloudDeal> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getName()), QscTaxCloudDeal::getName, bo.getName());
        lqw.eq(bo.getValue() != null, QscTaxCloudDeal::getValue, bo.getValue());
        lqw.eq(bo.getUserId() != null, QscTaxCloudDeal::getUserId, bo.getUserId());
        lqw.eq(bo.getDeptId() != null, QscTaxCloudDeal::getDeptId, bo.getDeptId());
        return lqw;
    }

    /**
     * 新增云厅办
     */
    @Override
    public Boolean insertByBo(QscTaxCloudDealBo bo) {
        QscTaxCloudDeal add = BeanUtil.toBean(bo, QscTaxCloudDeal.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改云厅办
     */
    @Override
    public Boolean updateByBo(QscTaxCloudDealBo bo) {
        QscTaxCloudDeal update = BeanUtil.toBean(bo, QscTaxCloudDeal.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(QscTaxCloudDeal entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除云厅办
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
