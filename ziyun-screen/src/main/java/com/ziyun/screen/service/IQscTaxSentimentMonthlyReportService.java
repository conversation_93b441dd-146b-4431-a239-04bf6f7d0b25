package com.ziyun.screen.service;

import com.ziyun.screen.domain.QscTaxSentimentMonthlyReport;
import com.ziyun.screen.domain.vo.QscTaxSentimentMonthlyReportVo;
import com.ziyun.screen.domain.bo.QscTaxSentimentMonthlyReportBo;
import com.ziyun.common.core.page.TableDataInfo;
import com.ziyun.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 税情感知月报Service接口
 *
 * <AUTHOR>
 * @date 2024-07-25
 */
public interface IQscTaxSentimentMonthlyReportService {

    /**
     * 查询税情感知月报
     */
    QscTaxSentimentMonthlyReportVo queryById(Long id);

    /**
     * 查询税情感知月报列表
     */
    TableDataInfo<QscTaxSentimentMonthlyReportVo> queryPageList(QscTaxSentimentMonthlyReportBo bo, PageQuery pageQuery);

    /**
     * 查询税情感知月报列表
     */
    List<QscTaxSentimentMonthlyReportVo> queryList(QscTaxSentimentMonthlyReportBo bo);

    /**
     * 新增税情感知月报
     */
    Boolean insertByBo(QscTaxSentimentMonthlyReportBo bo);

    /**
     * 修改税情感知月报
     */
    Boolean updateByBo(QscTaxSentimentMonthlyReportBo bo);

    /**
     * 校验并批量删除税情感知月报信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
