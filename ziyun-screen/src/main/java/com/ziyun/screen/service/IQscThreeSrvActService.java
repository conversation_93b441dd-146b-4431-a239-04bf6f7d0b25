package com.ziyun.screen.service;

import com.ziyun.screen.domain.QscThreeSrvAct;
import com.ziyun.screen.domain.vo.QscThreeSrvActVo;
import com.ziyun.screen.domain.bo.QscThreeSrvActBo;
import com.ziyun.common.core.page.TableDataInfo;
import com.ziyun.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 三服务活动集锦Service接口
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
public interface IQscThreeSrvActService {

    /**
     * 查询三服务活动集锦
     */
    QscThreeSrvActVo queryById(Long id);

    /**
     * 查询三服务活动集锦列表
     */
    TableDataInfo<QscThreeSrvActVo> queryPageList(QscThreeSrvActBo bo, PageQuery pageQuery);

    /**
     * 查询三服务活动集锦列表
     */
    List<QscThreeSrvActVo> queryList(QscThreeSrvActBo bo);

    /**
     * 新增三服务活动集锦
     */
    Boolean insertByBo(QscThreeSrvActBo bo);

    /**
     * 修改三服务活动集锦
     */
    Boolean updateByBo(QscThreeSrvActBo bo);

    /**
     * 校验并批量删除三服务活动集锦信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
