package com.ziyun.screen.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.ziyun.common.utils.StringUtils;
import com.ziyun.common.core.page.TableDataInfo;
import com.ziyun.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ziyun.screen.domain.bo.QscTaxIncomeTopBo;
import com.ziyun.screen.domain.vo.QscTaxIncomeTopVo;
import com.ziyun.screen.domain.QscTaxIncomeTop;
import com.ziyun.screen.mapper.QscTaxIncomeTopMapper;
import com.ziyun.screen.service.IQscTaxIncomeTopService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 税收排名Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
@RequiredArgsConstructor
@Service
@DS("master")
public class QscTaxIncomeTopServiceImpl implements IQscTaxIncomeTopService {

    private final QscTaxIncomeTopMapper baseMapper;

    /**
     * 查询税收排名
     */
    @Override
    public QscTaxIncomeTopVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询税收排名列表
     */
    @Override
    public TableDataInfo<QscTaxIncomeTopVo> queryPageList(QscTaxIncomeTopBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<QscTaxIncomeTop> lqw = buildQueryWrapper(bo);
        Page<QscTaxIncomeTopVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询税收排名列表
     */
    @Override
    public List<QscTaxIncomeTopVo> queryList(QscTaxIncomeTopBo bo) {
        LambdaQueryWrapper<QscTaxIncomeTop> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<QscTaxIncomeTop> buildQueryWrapper(QscTaxIncomeTopBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<QscTaxIncomeTop> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getDimensions()), QscTaxIncomeTop::getDimensions, bo.getDimensions());
        lqw.eq(bo.getBarData() != null, QscTaxIncomeTop::getBarData, bo.getBarData());
        lqw.eq(bo.getLineData() != null, QscTaxIncomeTop::getLineData, bo.getLineData());
        lqw.eq(bo.getUserId() != null, QscTaxIncomeTop::getUserId, bo.getUserId());
        lqw.eq(bo.getDeptId() != null, QscTaxIncomeTop::getDeptId, bo.getDeptId());
        return lqw;
    }

    /**
     * 新增税收排名
     */
    @Override
    public Boolean insertByBo(QscTaxIncomeTopBo bo) {
        QscTaxIncomeTop add = BeanUtil.toBean(bo, QscTaxIncomeTop.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改税收排名
     */
    @Override
    public Boolean updateByBo(QscTaxIncomeTopBo bo) {
        QscTaxIncomeTop update = BeanUtil.toBean(bo, QscTaxIncomeTop.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(QscTaxIncomeTop entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除税收排名
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
