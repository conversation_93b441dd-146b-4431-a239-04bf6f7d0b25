package com.ziyun.screen.service;

import com.ziyun.screen.domain.QscUnitOverView;
import com.ziyun.screen.domain.vo.QscUnitOverViewVo;
import com.ziyun.screen.domain.bo.QscUnitOverViewBo;
import com.ziyun.common.core.page.TableDataInfo;
import com.ziyun.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 大厅信息总览Service接口
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
public interface IQscUnitOverViewService {

    /**
     * 查询大厅信息总览
     */
    QscUnitOverViewVo queryById(Long id);

    /**
     *
     */
    QscUnitOverViewVo queryByUnitCode(String unitCode);

    /**
     * 查询大厅信息总览列表
     */
    TableDataInfo<QscUnitOverViewVo> queryPageList(QscUnitOverViewBo bo, PageQuery pageQuery);

    /**
     * 查询大厅信息总览列表
     */
    List<QscUnitOverViewVo> queryList(QscUnitOverViewBo bo);

    /**
     * 新增大厅信息总览
     */
    Boolean insertByBo(QscUnitOverViewBo bo);

    /**
     * 修改大厅信息总览
     */
    Boolean updateByBo(QscUnitOverViewBo bo);

    /**
     * 校验并批量删除大厅信息总览信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
