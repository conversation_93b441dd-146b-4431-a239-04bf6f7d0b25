package com.ziyun.screen.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.ziyun.common.utils.StringUtils;
import com.ziyun.common.core.page.TableDataInfo;
import com.ziyun.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ziyun.screen.domain.bo.QscMapUnitPointBo;
import com.ziyun.screen.domain.vo.QscMapUnitPointVo;
import com.ziyun.screen.domain.QscMapUnitPoint;
import com.ziyun.screen.mapper.QscMapUnitPointMapper;
import com.ziyun.screen.service.IQscMapUnitPointService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 地图点位信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
@RequiredArgsConstructor
@Service
@DS("master")
public class QscMapUnitPointServiceImpl implements IQscMapUnitPointService {

    private final QscMapUnitPointMapper baseMapper;

    /**
     * 查询地图点位信息
     */
    @Override
    public QscMapUnitPointVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询地图点位信息列表
     */
    @Override
    public TableDataInfo<QscMapUnitPointVo> queryPageList(QscMapUnitPointBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<QscMapUnitPoint> lqw = buildQueryWrapper(bo);
        Page<QscMapUnitPointVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询地图点位信息列表
     */
    @Override
    public List<QscMapUnitPointVo> queryList(QscMapUnitPointBo bo) {
        LambdaQueryWrapper<QscMapUnitPoint> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<QscMapUnitPoint> buildQueryWrapper(QscMapUnitPointBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<QscMapUnitPoint> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getUnitCode()), QscMapUnitPoint::getUnitCode, bo.getUnitCode());
        lqw.like(StringUtils.isNotBlank(bo.getName()), QscMapUnitPoint::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getPanlLeft()), QscMapUnitPoint::getPanlLeft, bo.getPanlLeft());
        lqw.eq(StringUtils.isNotBlank(bo.getPanlTop()), QscMapUnitPoint::getPanlTop, bo.getPanlTop());
        lqw.eq(StringUtils.isNotBlank(bo.getBgColor()), QscMapUnitPoint::getBgColor, bo.getBgColor());
        lqw.eq(StringUtils.isNotBlank(bo.getAddress()), QscMapUnitPoint::getAddress, bo.getAddress());
        lqw.eq(StringUtils.isNotBlank(bo.getPhoneNum()), QscMapUnitPoint::getPhoneNum, bo.getPhoneNum());
        lqw.eq(StringUtils.isNotBlank(bo.getSwjgdm()), QscMapUnitPoint::getSwjgdm, bo.getSwjgdm());
        lqw.eq(bo.getUserId() != null, QscMapUnitPoint::getUserId, bo.getUserId());
        lqw.eq(bo.getDeptId() != null, QscMapUnitPoint::getDeptId, bo.getDeptId());
        return lqw;
    }

    /**
     * 新增地图点位信息
     */
    @Override
    public Boolean insertByBo(QscMapUnitPointBo bo) {
        QscMapUnitPoint add = BeanUtil.toBean(bo, QscMapUnitPoint.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改地图点位信息
     */
    @Override
    public Boolean updateByBo(QscMapUnitPointBo bo) {
        QscMapUnitPoint update = BeanUtil.toBean(bo, QscMapUnitPoint.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(QscMapUnitPoint entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除地图点位信息
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
