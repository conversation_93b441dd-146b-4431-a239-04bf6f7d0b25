package com.ziyun.screen.service;

import com.ziyun.screen.domain.QscTaxIncomeTop;
import com.ziyun.screen.domain.vo.QscTaxIncomeTopVo;
import com.ziyun.screen.domain.bo.QscTaxIncomeTopBo;
import com.ziyun.common.core.page.TableDataInfo;
import com.ziyun.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 税收排名Service接口
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
public interface IQscTaxIncomeTopService {

    /**
     * 查询税收排名
     */
    QscTaxIncomeTopVo queryById(Long id);

    /**
     * 查询税收排名列表
     */
    TableDataInfo<QscTaxIncomeTopVo> queryPageList(QscTaxIncomeTopBo bo, PageQuery pageQuery);

    /**
     * 查询税收排名列表
     */
    List<QscTaxIncomeTopVo> queryList(QscTaxIncomeTopBo bo);

    /**
     * 新增税收排名
     */
    Boolean insertByBo(QscTaxIncomeTopBo bo);

    /**
     * 修改税收排名
     */
    Boolean updateByBo(QscTaxIncomeTopBo bo);

    /**
     * 校验并批量删除税收排名信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
