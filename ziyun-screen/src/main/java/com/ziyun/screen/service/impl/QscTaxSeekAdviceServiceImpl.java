package com.ziyun.screen.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.ziyun.common.utils.StringUtils;
import com.ziyun.common.core.page.TableDataInfo;
import com.ziyun.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ziyun.screen.domain.bo.QscTaxSeekAdviceBo;
import com.ziyun.screen.domain.vo.QscTaxSeekAdviceVo;
import com.ziyun.screen.domain.QscTaxSeekAdvice;
import com.ziyun.screen.mapper.QscTaxSeekAdviceMapper;
import com.ziyun.screen.service.IQscTaxSeekAdviceService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 咨询热点Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
@RequiredArgsConstructor
@Service
@DS("master")
public class QscTaxSeekAdviceServiceImpl implements IQscTaxSeekAdviceService {

    private final QscTaxSeekAdviceMapper baseMapper;

    /**
     * 查询咨询热点
     */
    @Override
    public QscTaxSeekAdviceVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询咨询热点列表
     */
    @Override
    public TableDataInfo<QscTaxSeekAdviceVo> queryPageList(QscTaxSeekAdviceBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<QscTaxSeekAdvice> lqw = buildQueryWrapper(bo);
        Page<QscTaxSeekAdviceVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询咨询热点列表
     */
    @Override
    public List<QscTaxSeekAdviceVo> queryList(QscTaxSeekAdviceBo bo) {
        LambdaQueryWrapper<QscTaxSeekAdvice> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<QscTaxSeekAdvice> buildQueryWrapper(QscTaxSeekAdviceBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<QscTaxSeekAdvice> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getDayCount() != null, QscTaxSeekAdvice::getDayCount, bo.getDayCount());
        lqw.eq(bo.getOlDayCount() != null, QscTaxSeekAdvice::getOlDayCount, bo.getOlDayCount());
        lqw.eq(bo.getMonthCount() != null, QscTaxSeekAdvice::getMonthCount, bo.getMonthCount());
        lqw.eq(bo.getOlMonthCount() != null, QscTaxSeekAdvice::getOlMonthCount, bo.getOlMonthCount());
        lqw.eq(bo.getUserId() != null, QscTaxSeekAdvice::getUserId, bo.getUserId());
        lqw.eq(bo.getDeptId() != null, QscTaxSeekAdvice::getDeptId, bo.getDeptId());
        return lqw;
    }

    /**
     * 新增咨询热点
     */
    @Override
    public Boolean insertByBo(QscTaxSeekAdviceBo bo) {
        QscTaxSeekAdvice add = BeanUtil.toBean(bo, QscTaxSeekAdvice.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改咨询热点
     */
    @Override
    public Boolean updateByBo(QscTaxSeekAdviceBo bo) {
        QscTaxSeekAdvice update = BeanUtil.toBean(bo, QscTaxSeekAdvice.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(QscTaxSeekAdvice entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除咨询热点
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
