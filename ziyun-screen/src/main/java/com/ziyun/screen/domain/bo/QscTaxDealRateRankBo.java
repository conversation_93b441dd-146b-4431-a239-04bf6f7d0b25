package com.ziyun.screen.domain.bo;

import com.ziyun.common.core.validate.AddGroup;
import com.ziyun.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;

import com.ziyun.common.core.domain.BaseEntity;

/**
 * 办税率排名业务对象 qsc_tax_deal_rate_rank
 *
 * <AUTHOR>
 * @date 2022-12-04
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class QscTaxDealRateRankBo extends BaseEntity {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 办理方式名称
     */
    @NotBlank(message = "办理方式名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String dealName;

    /**
     * 办税率
     */
//    @NotBlank(message = "办税率不能为空", groups = { AddGroup.class, EditGroup.class })
    private String dealRate;

    /**
     * 区办税率
     */
    @NotBlank(message = "区办税率不能为空", groups = { AddGroup.class, EditGroup.class })
    private String sectionRate;

    /**
     * 全市平均水平
     */
    private String cityAvg;

    /**
     * 全市排名
     */
    private Long listRank;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 部门ID
     */
    @NotNull(message = "部门ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long deptId;


}
