package com.ziyun.screen.domain.bo;

import com.ziyun.common.core.validate.AddGroup;
import com.ziyun.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ziyun.common.core.domain.BaseEntity;

/**
 * 电话热点咨询业务对象 qsc_phone_tag_analysis
 *
 * <AUTHOR>
 * @date 2022-12-04
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class QscPhoneTagAnalysisBo extends BaseEntity {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 时间维度(单位:月)
     */
    @NotNull(message = "时间维度(单位:月)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date anaylsisTime;

    /**
     * 咨询热点事项
     */
    @NotBlank(message = "咨询热点事项不能为空", groups = { AddGroup.class, EditGroup.class })
    private String hotName;

    /**
     * 咨询量
     */
    @NotNull(message = "咨询量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long hotVol;

    /**
     * 咨询占比
     */
    @NotNull(message = "咨询占比不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long hotRate;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 部门ID
     */
    @NotNull(message = "部门ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long deptId;


}
