package com.ziyun.screen.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.ziyun.common.core.domain.BaseEntity;

/**
 * 税局信息总览对象 qsc_tax_hall_overview
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("qsc_tax_hall_overview")
public class QscTaxHallOverview extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 税局简介
     */
    private String taxHallInfo;
    /**
     * 相关图片
     */
    private String infoImageUrl;
    /**
     * 纳税人
     */
    private Long taxpayers;
    /**
     * 单位纳税人
     */
    private Long establishmentsTaxpayers;
    /**
     * 一般纳税人
     */
    private Long generalTaxpayers;
    /**
     * 缴费人
     */
    private Long payers;
    /**
     * 个人经营纳税人
     */
    private Long personalBusinessTaxpayers;
    /**
     * 小规模纳税人
     */
    private Long smallScaleTaxpayers;
    /**
     * 同比(单位:%)
     */
    private String increaseRate;
    /**
     * 年度收入
     */
    private String allYearIncome;
    /**
     * 序号
     */
    private String unitCode;
    /**
     * 税务机关代码
     */
    private String swjgdm;
    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 部门ID
     */
    private Long deptId;

}
