package com.ziyun.screen.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ziyun.common.annotation.ExcelDictFormat;
import com.ziyun.common.convert.ExcelDictConvert;
import lombok.Data;
import java.util.Date;



/**
 * 纳税服务之星视图对象 qsc_tax_service_star
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
@Data
@ExcelIgnoreUnannotated
public class QscTaxServiceStarVo {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 登记序号
     */
    @ExcelProperty(value = "登记序号")
    private String unitCode;

    /**
     * 服务之星
     */
    @ExcelProperty(value = "服务之星")
    private String starName;

    /**
     * 图片
     */
    @ExcelProperty(value = "图片")
    private String starImageurl;

    /**
     * 详情
     */
    @ExcelProperty(value = "详情")
    private String starContent;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 部门ID
     */
    @ExcelProperty(value = "部门ID")
    private Long deptId;


}
