package com.ziyun.screen.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.ziyun.common.core.domain.BaseEntity;

/**
 * 智慧办税服务指数对象 qsc_tax_smart_service_index
 *
 * <AUTHOR>
 * @date 2022-11-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("qsc_tax_smart_service_index")
public class QscTaxSmartServiceIndex extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * id
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 智慧办税服务
     */
    private String indexName;
    /**
     * 感知指数
     */
    private String indexValue;
    /**
     * 指数左偏移
     */
    private String indexLeft;
    /**
     * 指数顶部偏移
     */
    private String idnexTop;

}
