package com.ziyun.screen.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ziyun.common.annotation.ExcelDictFormat;
import com.ziyun.common.convert.ExcelDictConvert;
import lombok.Data;
import java.util.Date;



/**
 * 地图点位信息视图对象 qsc_map_unit_point
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
@Data
@ExcelIgnoreUnannotated
public class QscMapUnitPointVo {

    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private Long id;

    /**
     * 序号
     */
    @ExcelProperty(value = "序号")
    private String unitCode;

    /**
     * 办税点名称
     */
    @ExcelProperty(value = "办税点名称")
    private String name;

    /**
     * 左侧偏移
     */
    @ExcelProperty(value = "左侧偏移")
    private String panlLeft;

    /**
     * 顶部偏移
     */
    @ExcelProperty(value = "顶部偏移")
    private String panlTop;

    /**
     * 背景颜色
     */
    @ExcelProperty(value = "背景颜色")
    private String bgColor;

    /**
     * 地址
     */
    @ExcelProperty(value = "地址")
    private String address;

    /**
     * 联系方式
     */
    @ExcelProperty(value = "联系方式")
    private String phoneNum;

    /**
     * 税务机关代码
     */
    @ExcelProperty(value = "税务机关代码")
    private String swjgdm;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 部门ID
     */
    @ExcelProperty(value = "部门ID")
    private Long deptId;


}
