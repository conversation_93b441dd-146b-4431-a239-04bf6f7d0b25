package com.ziyun.screen.domain.bo;

import com.ziyun.common.core.validate.AddGroup;
import com.ziyun.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ziyun.common.core.domain.BaseEntity;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 税情感知月报业务对象 qsc_tax_sentiment_monthly_report
 *
 * <AUTHOR>
 * @date 2024-07-25
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class QscTaxSentimentMonthlyReportBo extends BaseEntity {

    /**
     *
     */
    @NotNull(message = "不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 报告日期
     */
    @NotNull(message = "报告日期不能为空", groups = { AddGroup.class, EditGroup.class })
//    @DateTimeFormat(pattern = "yyyy-MM")
    private Date reportDate;

    /**
     * 图片URL数组
     */
    @NotBlank(message = "图片URL数组不能为空", groups = { AddGroup.class, EditGroup.class })
    private String imageUrls;

    /**
     * 月报文本内容
     */
    @NotBlank(message = "月报文本内容不能为空", groups = { AddGroup.class, EditGroup.class })
    private String textContent;

    /**
     * 备注
     */
//    @NotBlank(message = "备注不能为空", groups = { AddGroup.class, EditGroup.class })
    private String remarks;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 部门ID
     */
    @NotNull(message = "部门ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long deptId;


}
