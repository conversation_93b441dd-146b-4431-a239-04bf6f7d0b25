package com.ziyun.screen.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ziyun.common.annotation.ExcelDictFormat;
import com.ziyun.common.convert.ExcelDictConvert;
import lombok.Data;
import java.util.Date;



/**
 * 智慧办税服务指数视图对象 qsc_tax_smart_service_index
 *
 * <AUTHOR>
 * @date 2022-11-08
 */
@Data
@ExcelIgnoreUnannotated
public class QscTaxSmartServiceIndexVo {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 智慧办税服务
     */
    @ExcelProperty(value = "智慧办税服务")
    private String indexName;

    /**
     * 感知指数
     */
    @ExcelProperty(value = "感知指数")
    private String indexValue;

    /**
     * 指数左偏移
     */
    @ExcelProperty(value = "指数左偏移")
    private String indexLeft;

    /**
     * 指数顶部偏移
     */
    @ExcelProperty(value = "指数顶部偏移")
    private String idnexTop;


}
