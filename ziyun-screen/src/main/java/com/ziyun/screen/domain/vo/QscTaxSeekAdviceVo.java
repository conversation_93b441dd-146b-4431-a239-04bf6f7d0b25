package com.ziyun.screen.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ziyun.common.annotation.ExcelDictFormat;
import com.ziyun.common.convert.ExcelDictConvert;
import lombok.Data;
import java.util.Date;



/**
 * 咨询热点视图对象 qsc_tax_seek_advice
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
@Data
@ExcelIgnoreUnannotated
public class QscTaxSeekAdviceVo {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 当日咨询热线办件量
     */
    @ExcelProperty(value = "当日咨询热线办件量")
    private Long dayCount;

    /**
     * 当日在线咨询办件量
     */
    @ExcelProperty(value = "当日在线咨询办件量")
    private Long olDayCount;

    /**
     * 当月咨询热线办件量
     */
    @ExcelProperty(value = "当月咨询热线办件量")
    private Long monthCount;

    /**
     * 当月在线咨询办件量
     */
    @ExcelProperty(value = "当月在线咨询办件量")
    private Long olMonthCount;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 部门ID
     */
    @ExcelProperty(value = "部门ID")
    private Long deptId;


}
