package com.ziyun.screen.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ziyun.common.annotation.ExcelDictFormat;
import com.ziyun.common.convert.ExcelDictConvert;
import lombok.Data;
import java.util.Date;



/**
 * 行业分类视图对象 qsc_tax_region_industry_tag_info
 *
 * <AUTHOR>
 * @date 2022-11-08
 */
@Data
@ExcelIgnoreUnannotated
public class QscTaxRegionIndustryTagInfoVo {

    private static final long serialVersionUID = 1L;

    /**
     * 区域ID
     */
    @ExcelProperty(value = "区域ID")
    private Long regionId;

    /**
     * 行业标签代码
     */
    @ExcelProperty(value = "行业标签代码")
    private String industryTag;

    /**
     * 行业标签名称
     */
    @ExcelProperty(value = "行业标签名称")
    private String industryTagName;

    /**
     * 企业数量
     */
    @ExcelProperty(value = "企业数量")
    private Long enterpriseNum;

    /**
     * 个体数量
     */
    @ExcelProperty(value = "个体数量")
    private Long individualNum;

    /**
     * 写入日期
     */
    @ExcelProperty(value = "写入日期")
    private Date writeDate;


}
