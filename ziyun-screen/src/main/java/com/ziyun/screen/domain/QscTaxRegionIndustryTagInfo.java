package com.ziyun.screen.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ziyun.common.core.domain.BaseEntity;

/**
 * 行业分类对象 qsc_tax_region_industry_tag_info
 *
 * <AUTHOR>
 * @date 2022-11-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("qsc_tax_region_industry_tag_info")
public class QscTaxRegionIndustryTagInfo extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 区域ID
     */
    private Long regionId;
    /**
     * 行业标签代码
     */
    private String industryTag;
    /**
     * 行业标签名称
     */
    private String industryTagName;
    /**
     * 企业数量
     */
    private Long enterpriseNum;
    /**
     * 个体数量
     */
    private Long individualNum;
    /**
     * 写入日期
     */
    private Date writeDate;

}
