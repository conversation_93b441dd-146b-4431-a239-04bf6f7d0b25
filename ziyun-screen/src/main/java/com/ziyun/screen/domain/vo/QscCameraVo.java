package com.ziyun.screen.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ziyun.common.annotation.ExcelDictFormat;
import com.ziyun.common.convert.ExcelDictConvert;
import lombok.Data;
import java.util.Date;



/**
 * 【请填写功能名称】视图对象 qsc_camera
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
@Data
@ExcelIgnoreUnannotated
public class QscCameraVo {

    private static final long serialVersionUID = 1L;

    /**
     * 摄像头ID
     */
    @ExcelProperty(value = "摄像头ID")
    private Long cameraId;

    /**
     * 摄像头用户名
     */
    @ExcelProperty(value = "摄像头用户名")
    private String username;

    /**
     * 摄像头登陆密码
     */
    @ExcelProperty(value = "摄像头登陆密码")
    private String password;

    /**
     * 摄像头IP地址
     */
    @ExcelProperty(value = "摄像头IP地址")
    private String ip;

    /**
     * 摄像头端口
     */
    @ExcelProperty(value = "摄像头端口")
    private Long port;

    /**
     * 摄像头访问地址
     */
    @ExcelProperty(value = "摄像头访问地址")
    private String url;

    /**
     * 摄像头厂商
     */
    @ExcelProperty(value = "摄像头厂商", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "qsc_camera_produce")
    private String cameraProduce;

    /**
     * 税务机关序号
     */
    @ExcelProperty(value = "税务机关序号")
    private String unitCode;

    /**
     * 摄像头名称
     */
    @ExcelProperty(value = "摄像头名称")
    private String cameraName;

    /**
     * 设备类型
     */
    @ExcelProperty(value = "设备类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "qsc_device_type")
    private String deviceType;

    /**
     * NVR地址
     */
    @ExcelProperty(value = "NVR地址")
    private String nvrPath;

    /**
     * NVR厂商
     */
    @ExcelProperty(value = "NVR厂商", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "qsc_nvr_produce")
    private String nvrProduce;

    /**
     * 回放地址
     */
    @ExcelProperty(value = "回放地址")
    private String playBack;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 部门ID
     */
    @ExcelProperty(value = "部门ID")
    private Long deptId;


}
