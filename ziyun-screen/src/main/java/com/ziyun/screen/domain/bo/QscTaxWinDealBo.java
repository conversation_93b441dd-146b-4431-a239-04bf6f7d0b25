package com.ziyun.screen.domain.bo;

import com.ziyun.common.core.validate.AddGroup;
import com.ziyun.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;

import com.ziyun.common.core.domain.BaseEntity;

/**
 * 窗口办业务对象 qsc_tax_win_deal
 *
 * <AUTHOR>
 * @date 2022-12-04
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class QscTaxWinDealBo extends BaseEntity {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 窗口办理事项
     */
    @NotBlank(message = "窗口办理事项不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 业务量(单位:笔)
     */
    @NotNull(message = "业务量(单位:笔)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long value;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 部门ID
     */
    @NotNull(message = "部门ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long deptId;


}
