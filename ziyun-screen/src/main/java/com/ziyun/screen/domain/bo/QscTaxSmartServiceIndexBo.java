package com.ziyun.screen.domain.bo;

import com.ziyun.common.core.validate.AddGroup;
import com.ziyun.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;

import com.ziyun.common.core.domain.BaseEntity;

/**
 * 智慧办税服务指数业务对象 qsc_tax_smart_service_index
 *
 * <AUTHOR>
 * @date 2022-11-08
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class QscTaxSmartServiceIndexBo extends BaseEntity {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 智慧办税服务
     */
    @NotBlank(message = "智慧办税服务不能为空", groups = { AddGroup.class, EditGroup.class })
    private String indexName;

    /**
     * 感知指数
     */
    @NotBlank(message = "感知指数不能为空", groups = { AddGroup.class, EditGroup.class })
    private String indexValue;

    /**
     * 指数左偏移
     */
    @NotBlank(message = "指数左偏移不能为空", groups = { AddGroup.class, EditGroup.class })
    private String indexLeft;

    /**
     * 指数顶部偏移
     */
    @NotBlank(message = "指数顶部偏移不能为空", groups = { AddGroup.class, EditGroup.class })
    private String idnexTop;


}
