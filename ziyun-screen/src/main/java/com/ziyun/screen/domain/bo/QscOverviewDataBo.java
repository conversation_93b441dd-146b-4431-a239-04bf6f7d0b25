package com.ziyun.screen.domain.bo;

import com.ziyun.common.core.validate.AddGroup;
import com.ziyun.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;

import com.ziyun.common.core.domain.BaseEntity;

/**
 * 税情分析数据总览业务对象 qsc_overview_data
 *
 * <AUTHOR>
 * @date 2022-12-04
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class QscOverviewDataBo extends BaseEntity {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 税情分析数据总览显示名称
     */
    @NotBlank(message = "税情分析数据总览显示名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 对应名称显示数据
     */
    @NotBlank(message = "对应名称显示数据不能为空", groups = { AddGroup.class, EditGroup.class })
    private String count;

    /**
     * 显示颜色
     */
    @NotBlank(message = "显示颜色不能为空", groups = { AddGroup.class, EditGroup.class })
    private String color;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 部门ID
     */
    @NotNull(message = "部门ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long deptId;


}
