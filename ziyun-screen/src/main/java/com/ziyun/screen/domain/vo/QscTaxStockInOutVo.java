package com.ziyun.screen.domain.vo;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ziyun.common.annotation.ExcelDictFormat;
import com.ziyun.common.convert.ExcelDictConvert;
import lombok.Data;
import java.util.Date;



/**
 * 入库出库视图对象 qsc_tax_stock_in_out
 *
 * <AUTHOR>
 * @date 2022-12-08
 */
@Data
@ExcelIgnoreUnannotated
public class QscTaxStockInOutVo {

    private static final long serialVersionUID = 1L;

    /**
     * stock_id
     */
    @ExcelProperty(value = "stock_id")
    private Long stockId;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 部门ID
     */
    @ExcelProperty(value = "部门ID")
    private Long deptId;

    /**
     * 街道id
     */
    @ExcelProperty(value = "街道id")
    private Long regionId;

    /**
     * 街道名称
     */
    @ExcelProperty(value = "街道名称")
    private String regionName;

    /**
     * 纳税人名称
     */
    @ExcelProperty(value = "纳税人名称")
    private String taxpayerName;

    /**
     * 信用等级
     */
    @ExcelProperty(value = "信用等级")
    private String evaluationResult;

    /**
     * 入库（万元）
     */
    @ExcelProperty(value = "入库", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "万=元")
    private BigDecimal stockIn;

    /**
     * 退税（万元）
     */
    @ExcelProperty(value = "退税", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "万=元")
    private BigDecimal stockOut;

    /**
     * 同比
     */
    @ExcelProperty(value = "同比")
    private String rate;

    /**
     * 导入日期
     */
    @ExcelProperty(value = "导入日期")
    private Date importDate;


}
