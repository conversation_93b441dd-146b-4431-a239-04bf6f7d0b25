package com.ziyun.screen.domain.bo;

import com.ziyun.common.core.validate.AddGroup;
import com.ziyun.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ziyun.common.core.domain.BaseEntity;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 月度业务量排名业务对象 qsc_tax_biz_top
 *
 * <AUTHOR>
 * @date 2022-12-04
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class QscTaxBizTopBo extends BaseEntity {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 时间维度
     */
    @NotNull(message = "时间维度不能为空", groups = { AddGroup.class, EditGroup.class })
    @DateTimeFormat(pattern = "yyyy-MM")
    private Date month;

    /**
     * 业务名称
     */
    @NotBlank(message = "业务名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String bizName;

    /**
     * 业务量
     */
    @NotNull(message = "业务量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long bizVol;

    /**
     * 业务量占比
     */
    @NotNull(message = "业务量占比不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long bizRatio;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 部门ID
     */
    @NotNull(message = "部门ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long deptId;


}
