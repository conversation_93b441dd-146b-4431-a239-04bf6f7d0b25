package com.ziyun.screen.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ziyun.common.core.domain.BaseEntity;

/**
 * 税情感知月报对象 qsc_tax_sentiment_monthly_report
 *
 * <AUTHOR>
 * @date 2024-07-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("qsc_tax_sentiment_monthly_report")
public class QscTaxSentimentMonthlyReport extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 报告日期
     */
    private Date reportDate;
    /**
     * 图片URL数组
     */
    private String imageUrls;
    /**
     * 月报文本内容
     */
    private String textContent;
    /**
     * 备注
     */
    private String remarks;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 部门ID
     */
    private Long deptId;

}
