package com.ziyun.screen.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ziyun.common.annotation.ExcelDictFormat;
import com.ziyun.common.convert.ExcelDictConvert;
import lombok.Data;
import java.util.Date;



/**
 * 【请填写功能名称】视图对象 qsc_nvr
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
@Data
@ExcelIgnoreUnannotated
public class QscNvrVo {

    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private Long nvrId;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private String ip;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private String username;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private String password;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private Long port;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private String unitCode;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private String deviceType;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private String nvrName;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private String nvrProduce;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 部门ID
     */
    @ExcelProperty(value = "部门ID")
    private Long deptId;


}
