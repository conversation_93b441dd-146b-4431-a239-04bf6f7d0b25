package com.ziyun.screen.domain.bo;

import com.ziyun.common.core.validate.AddGroup;
import com.ziyun.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ziyun.common.core.domain.BaseEntity;

/**
 * 入库出库业务对象 qsc_tax_stock_in_out
 *
 * <AUTHOR>
 * @date 2022-12-08
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class QscTaxStockInOutBo extends BaseEntity {

    /**
     * stock_id
     */
    @NotNull(message = "stock_id不能为空", groups = { EditGroup.class })
    private Long stockId;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空", groups = {EditGroup.class })
    private Long userId;

    /**
     * 部门ID
     */
    @NotNull(message = "部门ID不能为空", groups = {EditGroup.class })
    private Long deptId;

    /**
     * 街道id
     */
    @NotNull(message = "街道id不能为空", groups = {EditGroup.class })
    private Long regionId;

    /**
     * 街道名称
     */
    @NotBlank(message = "街道名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String regionName;

    /**
     * 纳税人名称
     */
    @NotBlank(message = "纳税人名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String taxpayerName;

    /**
     * 信用等级
     */
    @NotBlank(message = "信用等级不能为空", groups = { AddGroup.class, EditGroup.class })
    private String evaluationResult;

    /**
     * 入库（万元）
     */
    @NotNull(message = "入库（万元）不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal stockIn;

    /**
     * 退税（万元）
     */
    @NotNull(message = "退税（万元）不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal stockOut;

    /**
     * 同比
     */
    @NotBlank(message = "同比不能为空", groups = { AddGroup.class, EditGroup.class })
    private String rate;

    /**
     * 导入日期
     */
    @NotNull(message = "导入日期不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date importDate;


}
