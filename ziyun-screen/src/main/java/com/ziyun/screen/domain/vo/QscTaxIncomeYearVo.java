package com.ziyun.screen.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ziyun.common.annotation.ExcelDictFormat;
import com.ziyun.common.convert.ExcelDictConvert;
import lombok.Data;
import java.util.Date;



/**
 * 税收收入(年)视图对象 qsc_tax_income_year
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
@Data
@ExcelIgnoreUnannotated
public class QscTaxIncomeYearVo {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 时间
     */
    @ExcelProperty(value = "时间")
    private Date incomeYear;

    /**
     * 收入
     */
    @ExcelProperty(value = "收入")
    private Long income;

    /**
     * 同比率(单位:%)
     */
    @ExcelProperty(value = "同比率(单位:%)")
    private Long rate;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 部门ID
     */
    @ExcelProperty(value = "部门ID")
    private Long deptId;


}
