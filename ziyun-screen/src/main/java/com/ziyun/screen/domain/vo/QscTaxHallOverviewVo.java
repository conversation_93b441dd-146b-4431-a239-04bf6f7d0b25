package com.ziyun.screen.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ziyun.common.annotation.ExcelDictFormat;
import com.ziyun.common.convert.ExcelDictConvert;
import lombok.Data;
import java.util.Date;



/**
 * 税局信息总览视图对象 qsc_tax_hall_overview
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
@Data
@ExcelIgnoreUnannotated
public class QscTaxHallOverviewVo {

    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private Long id;

    /**
     * 税局简介
     */
    @ExcelProperty(value = "税局简介")
    private String taxHallInfo;

    /**
     * 相关图片
     */
    @ExcelProperty(value = "相关图片")
    private String infoImageUrl;

    /**
     * 纳税人
     */
    @ExcelProperty(value = "纳税人")
    private Long taxpayers;

    /**
     * 单位纳税人
     */
    @ExcelProperty(value = "单位纳税人")
    private Long establishmentsTaxpayers;

    /**
     * 一般纳税人
     */
    @ExcelProperty(value = "一般纳税人")
    private Long generalTaxpayers;

    /**
     * 缴费人
     */
    @ExcelProperty(value = "缴费人")
    private Long payers;

    /**
     * 个人经营纳税人
     */
    @ExcelProperty(value = "个人经营纳税人")
    private Long personalBusinessTaxpayers;

    /**
     * 小规模纳税人
     */
    @ExcelProperty(value = "小规模纳税人")
    private Long smallScaleTaxpayers;

    /**
     * 同比(单位:%)
     */
    @ExcelProperty(value = "同比(单位:%)")
    private String increaseRate;

    /**
     * 年度收入
     */
    @ExcelProperty(value = "年度收入")
    private String allYearIncome;

    /**
     * 序号
     */
    @ExcelProperty(value = "序号")
    private String unitCode;

    /**
     * 税务机关代码
     */
    @ExcelProperty(value = "税务机关代码")
    private String swjgdm;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 部门ID
     */
    @ExcelProperty(value = "部门ID")
    private Long deptId;


}
