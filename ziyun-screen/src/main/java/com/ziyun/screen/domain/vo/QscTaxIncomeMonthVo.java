package com.ziyun.screen.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ziyun.common.annotation.ExcelDictFormat;
import com.ziyun.common.convert.ExcelDictConvert;
import lombok.Data;
import java.util.Date;



/**
 * 税收收入(月)视图对象 qsc_tax_income_month
 *
 * <AUTHOR>
 * @date 2022-12-06
 */
@Data
@ExcelIgnoreUnannotated
public class QscTaxIncomeMonthVo {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 部门ID
     */
    @ExcelProperty(value = "部门ID")
    private Long deptId;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 时间
     */
    @ExcelProperty(value = "时间")
    private Date incomeMonth;

    /**
     * 税收收入
     */
    @ExcelProperty(value = "税收收入")
    private Double taxIncome;

    /**
     * 非税收入
     */
    @ExcelProperty(value = "非税收入")
    private Double nonTaxIncome;


}
