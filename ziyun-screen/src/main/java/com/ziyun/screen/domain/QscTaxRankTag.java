package com.ziyun.screen.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.ziyun.common.core.domain.BaseEntity;

/**
 * 好差评标签对象 qsc_tax_rank_tag
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("qsc_tax_rank_tag")
public class QscTaxRankTag extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * id
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 评价标签
     */
    private String tagName;
    /**
     * 斗门
     */
    private Long taxHallOne;
    /**
     * 稽山
     */
    private Long taxHallTwo;
    /**
     * 鉴湖
     */
    private Long taxHallThr;
    /**
     * 个人业务
     */
    private Long bizPer;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 部门ID
     */
    private Long deptId;

}
