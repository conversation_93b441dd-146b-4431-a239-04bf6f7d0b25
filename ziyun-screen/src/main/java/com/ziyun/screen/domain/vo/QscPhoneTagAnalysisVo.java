package com.ziyun.screen.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ziyun.common.annotation.ExcelDictFormat;
import com.ziyun.common.convert.ExcelDictConvert;
import lombok.Data;
import java.util.Date;



/**
 * 电话热点咨询视图对象 qsc_phone_tag_analysis
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
@Data
@ExcelIgnoreUnannotated
public class QscPhoneTagAnalysisVo {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 时间维度(单位:月)
     */
    @ExcelProperty(value = "时间维度(单位:月)")
    private Date anaylsisTime;

    /**
     * 咨询热点事项
     */
    @ExcelProperty(value = "咨询热点事项")
    private String hotName;

    /**
     * 咨询量
     */
    @ExcelProperty(value = "咨询量")
    private Long hotVol;

    /**
     * 咨询占比
     */
    @ExcelProperty(value = "咨询占比")
    private Long hotRate;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 部门ID
     */
    @ExcelProperty(value = "部门ID")
    private Long deptId;


}
