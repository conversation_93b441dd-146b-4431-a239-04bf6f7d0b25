package com.ziyun.screen.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.ziyun.common.core.domain.BaseEntity;

/**
 * 来电情况分析对象 qsc_phone_analysis
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("qsc_phone_analysis")
public class QscPhoneAnalysis extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     *
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 平均接听时长
     * 修改后为电话接听率
     */
    private Long callTimeAvg;
    /**
     * 等待接听人数
     * 修改后为呼损回访量
     */
    private Long waitingCallinCount;
    /**
     * 平均等待时长
     * 修改后为呼入满意率
     */
    private Long waitingCallinTimeAvg;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 部门ID
     */
    private Long deptId;

}
