package com.ziyun.screen.domain.bo;

import com.ziyun.common.core.validate.AddGroup;
import com.ziyun.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;

import com.ziyun.common.core.domain.BaseEntity;

/**
 * 来电情况分析业务对象 qsc_phone_analysis
 *
 * <AUTHOR>
 * @date 2022-12-04
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class QscPhoneAnalysisBo extends BaseEntity {

    /**
     *
     */
    @NotNull(message = "不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 平均接听时长
     * 修改后为电话接听率
     */
    @NotNull(message = "电话接听率不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long callTimeAvg;

    /**
     * 等待接听人数
     * 修改后为呼损回访量
     */
    @NotNull(message = "呼损回访量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long waitingCallinCount;

    /**
     * 平均等待时长
     * 修改后为呼入满意率
     */
    @NotNull(message = "呼入满意率不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long waitingCallinTimeAvg;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 部门ID
     */
    @NotNull(message = "部门ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long deptId;


}
