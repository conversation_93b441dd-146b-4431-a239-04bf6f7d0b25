package com.ziyun.screen.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.ziyun.common.core.domain.BaseEntity;

/**
 * 办税率排名对象 qsc_tax_deal_rate_rank
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("qsc_tax_deal_rate_rank")
public class QscTaxDealRateRank extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * id
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 办理方式名称
     */
    private String dealName;
    /**
     * 办税率
     */
    private String dealRate;
    /**
     * 区办税率
     */
    private String sectionRate;
    /**
     * 全市平均水平
     */
    private String cityAvg;
    /**
     * 全市排名
     */
    private Long listRank;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 部门ID
     */
    private Long deptId;

}
