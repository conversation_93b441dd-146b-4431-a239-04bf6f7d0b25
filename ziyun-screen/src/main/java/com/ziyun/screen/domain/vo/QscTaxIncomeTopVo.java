package com.ziyun.screen.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ziyun.common.annotation.ExcelDictFormat;
import com.ziyun.common.convert.ExcelDictConvert;
import lombok.Data;
import java.util.Date;



/**
 * 税收排名视图对象 qsc_tax_income_top
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
@Data
@ExcelIgnoreUnannotated
public class QscTaxIncomeTopVo {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 税收事项
     */
    @ExcelProperty(value = "税收事项")
    private String dimensions;

    /**
     * 税收收入(单位:亿元)
     */
    @ExcelProperty(value = "税收收入(单位:亿元)")
    private Long barData;

    /**
     * 同比率(单位:%)
     */
    @ExcelProperty(value = "同比率(单位:%)")
    private Long lineData;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 部门ID
     */
    @ExcelProperty(value = "部门ID")
    private Long deptId;


}
