package com.ziyun.screen.domain.bo;

import com.ziyun.common.core.validate.AddGroup;
import com.ziyun.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;

import com.ziyun.common.core.domain.BaseEntity;

/**
 * 大厅信息总览业务对象 qsc_unit_over_view
 *
 * <AUTHOR>
 * @date 2022-12-04
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class QscUnitOverViewBo extends BaseEntity {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 大厅登记序号
     */
    @NotNull(message = "大厅登记序号不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long unitCode;

    /**
     * 窗口数量
     */
    @NotNull(message = "窗口数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long winNum;

    /**
     * 人员数量
     */
    @NotNull(message = "人员数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long empNum;

    /**
     * 同比增长
     */
    @NotNull(message = "同比增长不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long rate;

    /**
     * 自助机数量
     */
    @NotNull(message = "自助机数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long diyNum;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 部门ID
     */
    @NotNull(message = "部门ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long deptId;


}
