package com.ziyun.screen.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ziyun.common.annotation.ExcelDictFormat;
import com.ziyun.common.convert.ExcelDictConvert;
import lombok.Data;
import java.util.Date;



/**
 * 税情分析数据总览视图对象 qsc_overview_data
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
@Data
@ExcelIgnoreUnannotated
public class QscOverviewDataVo {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 税情分析数据总览显示名称
     */
    @ExcelProperty(value = "税情分析数据总览显示名称")
    private String name;

    /**
     * 对应名称显示数据
     */
    @ExcelProperty(value = "对应名称显示数据")
    private String count;

    /**
     * 显示颜色
     */
    @ExcelProperty(value = "显示颜色")
    private String color;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 部门ID
     */
    @ExcelProperty(value = "部门ID")
    private Long deptId;


}
