package com.ziyun.screen.domain.bo;

import com.ziyun.common.core.validate.AddGroup;
import com.ziyun.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ziyun.common.core.domain.BaseEntity;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 业务量总览业务对象 qsc_tax_biz_vol
 *
 * <AUTHOR>
 * @date 2022-12-04
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class QscTaxBizVolBo extends BaseEntity {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 时间维度(单位:月)
     */

    @NotNull(message = "时间维度(单位:月)不能为空", groups = { AddGroup.class, EditGroup.class })
    @DateTimeFormat(pattern = "yyyy-MM")
    private Date bizMonthly;

    /**
     * 业务总量
     */
    @NotNull(message = "业务总量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long bizMonthlyVol;

    /**
     * 同比率(单位:%)
     */
    @NotBlank(message = "同比率(单位:%)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String bizMonthlyRatio;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 部门ID
     */
    @NotNull(message = "部门ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long deptId;


}
