<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ziyun.screen.mapper.QscRegionMapper">

    <resultMap type="com.ziyun.screen.domain.QscRegion" id="QscRegionResult">
        <result property="id" column="id"/>
        <result property="pid" column="pid"/>
        <result property="deep" column="deep"/>
        <result property="name" column="name"/>
        <result property="pinyinPrefix" column="pinyin_prefix"/>
        <result property="pinyin" column="pinyin"/>
        <result property="extId" column="ext_id"/>
        <result property="extName" column="ext_name"/>
    </resultMap>

    <sql id="selectRegionVo">
        select *
        from qsc_region r
    </sql>

    <select id="selectRegionByExtName" parameterType="String" resultMap="QscRegionResult">
        <include refid="selectRegionVo"/>
        where r.pid = '330602' and r.ext_name like #{extName}
    </select>

</mapper>
