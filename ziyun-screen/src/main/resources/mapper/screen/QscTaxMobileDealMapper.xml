<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ziyun.screen.mapper.QscTaxMobileDealMapper">

    <resultMap type="com.ziyun.screen.domain.QscTaxMobileDeal" id="QscTaxMobileDealResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="value" column="value"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="userId" column="user_id"/>
        <result property="deptId" column="dept_id"/>
    </resultMap>

    <select id="customPageList" resultType="com.ziyun.screen.domain.vo.QscTaxMobileDealVo">
        SELECT * FROM qsc_tax_mobile_deal ${ew.customSqlSegment}
    </select>
</mapper>
