<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ziyun.screen.mapper.QscTaxCommDataMapper">

    <resultMap type="com.ziyun.screen.domain.QscTaxCommData" id="QscTaxCommDataResult">
        <result property="id" column="id"/>
        <result property="companyCount" column="company_count"/>
        <result property="activationCount" column="activation_count"/>
        <result property="activationRate" column="activation_rate"/>
        <result property="groupCount" column="group_count"/>
        <result property="msgSendCount" column="msg_send_count"/>
        <result property="msgSendPeopleCount" column="msg_send_people_count"/>
        <result property="msgReadPeopleCount" column="msg_read_people_count"/>
        <result property="onAirCount" column="on_air_Count"/>
        <result property="onAirWatchCount" column="on_air_watch_count"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="userId" column="user_id"/>
        <result property="deptId" column="dept_id"/>
    </resultMap>


</mapper>
