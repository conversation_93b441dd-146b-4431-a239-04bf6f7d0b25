<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ziyun.screen.mapper.QscTaxRankTagMapper">

    <resultMap type="com.ziyun.screen.domain.QscTaxRankTag" id="QscTaxRankTagResult">
        <result property="id" column="id"/>
        <result property="tagName" column="tag_name"/>
        <result property="taxHallOne" column="tax_hall_one"/>
        <result property="taxHallTwo" column="tax_hall_two"/>
        <result property="taxHallThr" column="tax_hall_thr"/>
        <result property="bizPer" column="biz_per"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="userId" column="user_id"/>
        <result property="deptId" column="dept_id"/>
    </resultMap>


    <resultMap id="TagV" type="com.ziyun.screen.domain.chart.TagV">
        <result column="tag_name" property="tagName" />
        <result column="max" property="max" />
    </resultMap>
</mapper>
