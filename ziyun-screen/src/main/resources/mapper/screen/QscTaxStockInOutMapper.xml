<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ziyun.screen.mapper.QscTaxStockInOutMapper">

    <resultMap type="com.ziyun.screen.domain.QscTaxStockInOut" id="QscTaxStockInOutResult">
        <result property="stockId" column="stock_id"/>
        <result property="userId" column="user_Id"/>
        <result property="deptId" column="dept_Id"/>
        <result property="regionId" column="region_id"/>
        <result property="regionName" column="region_name"/>
        <result property="taxpayerName" column="taxpayer_name"/>
        <result property="evaluationResult" column="evaluation_result"/>
        <result property="stockIn" column="stock_in"/>
        <result property="stockOut" column="stock_out"/>
        <result property="rate" column="rate"/>
        <result property="importDate" column="import_date"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectQscTaxStockInOutVo">
        select * FROM qsc_tax_stock_in_out
    </sql>

    <select id="selectStockByTaxPayerName" parameterType="String" resultMap="QscTaxStockInOutResult">
        --         <include refid="selectQscTaxStockInOutVo"/>
        where taxpayer_name = #{taxpayer_name}
    </select>
</mapper>
