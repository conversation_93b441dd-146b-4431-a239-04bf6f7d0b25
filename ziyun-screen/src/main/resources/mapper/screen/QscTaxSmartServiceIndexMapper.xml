<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ziyun.screen.mapper.QscTaxSmartServiceIndexMapper">

    <resultMap type="com.ziyun.screen.domain.QscTaxSmartServiceIndex" id="QscTaxSmartServiceIndexResult">
        <result property="id" column="id"/>
        <result property="indexName" column="index_name"/>
        <result property="indexValue" column="index_value"/>
        <result property="indexLeft" column="index_left"/>
        <result property="idnexTop" column="idnex_top"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


</mapper>
