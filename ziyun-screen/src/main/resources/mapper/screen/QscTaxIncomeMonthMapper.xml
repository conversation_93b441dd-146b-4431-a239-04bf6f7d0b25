<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ziyun.screen.mapper.QscTaxIncomeMonthMapper">

    <resultMap type="com.ziyun.screen.domain.QscTaxIncomeMonth" id="QscTaxIncomeMonthResult">
        <result property="id" column="id"/>
        <result property="deptId" column="dept_id"/>
        <result property="userId" column="user_id"/>
        <result property="incomeMonth" column="income_month"/>
        <result property="taxIncome" column="tax_income"/>
        <result property="nonTaxIncome" column="non_tax_income"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="customPageList" resultType="com.ziyun.screen.domain.vo.QscTaxIncomeMonthVo">
        SELECT * FROM qsc_tax_income_month ${ew.customSqlSegment}
    </select>


    <select id="queryByDate" parameterType="java.util.Date" resultMap="QscTaxIncomeMonthResult">
        select * FROM qsc_tax_income_month
        where tax_income = #{tax_income}
    </select>
</mapper>
