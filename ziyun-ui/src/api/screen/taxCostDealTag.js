import request from '@/utils/request'

// 查询税费协同工单标签列表
export function listTaxCostDealTag(query) {
  return request({
    url: '/screen/taxCostDealTag/list',
    method: 'get',
    params: query
  })
}

// 查询税费协同工单标签详细
export function getTaxCostDealTag(id) {
  return request({
    url: '/screen/taxCostDealTag/' + id,
    method: 'get'
  })
}

// 新增税费协同工单标签
export function addTaxCostDealTag(data) {
  return request({
    url: '/screen/taxCostDealTag',
    method: 'post',
    data: data
  })
}

// 修改税费协同工单标签
export function updateTaxCostDealTag(data) {
  return request({
    url: '/screen/taxCostDealTag',
    method: 'put',
    data: data
  })
}

// 删除税费协同工单标签
export function delTaxCostDealTag(id) {
  return request({
    url: '/screen/taxCostDealTag/' + id,
    method: 'delete'
  })
}
