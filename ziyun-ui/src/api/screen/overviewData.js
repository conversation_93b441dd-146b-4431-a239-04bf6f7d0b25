import request from '@/utils/request'

// 查询税情分析数据总览列表
export function listOverviewData(query) {
  return request({
    url: '/screen/overviewData/list',
    method: 'get',
    params: query
  })
}

// 查询税情分析数据总览详细
export function getOverviewData(id) {
  return request({
    url: '/screen/overviewData/' + id,
    method: 'get'
  })
}

// 新增税情分析数据总览
export function addOverviewData(data) {
  return request({
    url: '/screen/overviewData',
    method: 'post',
    data: data
  })
}

// 修改税情分析数据总览
export function updateOverviewData(data) {
  return request({
    url: '/screen/overviewData',
    method: 'put',
    data: data
  })
}

// 删除税情分析数据总览
export function delOverviewData(id) {
  return request({
    url: '/screen/overviewData/' + id,
    method: 'delete'
  })
}
