import request from '@/utils/request'

// 查询好差评数据渠道列表
export function listTaxRankChannel(query) {
  return request({
    url: '/screen/taxRankChannel/list',
    method: 'get',
    params: query
  })
}

// 查询好差评数据渠道详细
export function getTaxRankChannel(id) {
  return request({
    url: '/screen/taxRankChannel/' + id,
    method: 'get'
  })
}

// 新增好差评数据渠道
export function addTaxRankChannel(data) {
  return request({
    url: '/screen/taxRankChannel',
    method: 'post',
    data: data
  })
}

// 修改好差评数据渠道
export function updateTaxRankChannel(data) {
  return request({
    url: '/screen/taxRankChannel',
    method: 'put',
    data: data
  })
}

// 删除好差评数据渠道
export function delTaxRankChannel(id) {
  return request({
    url: '/screen/taxRankChannel/' + id,
    method: 'delete'
  })
}
