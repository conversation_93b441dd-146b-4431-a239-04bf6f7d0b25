import request from '@/utils/request'

// 查询【请填写功能名称】列表
export function listNvr(query) {
  return request({
    url: '/screen/nvr/list',
    method: 'get',
    params: query
  })
}

// 查询【请填写功能名称】详细
export function getNvr(nvrId) {
  return request({
    url: '/screen/nvr/' + nvrId,
    method: 'get'
  })
}

// 新增【请填写功能名称】
export function addNvr(data) {
  return request({
    url: '/screen/nvr',
    method: 'post',
    data: data
  })
}

// 修改【请填写功能名称】
export function updateNvr(data) {
  return request({
    url: '/screen/nvr',
    method: 'put',
    data: data
  })
}

// 删除【请填写功能名称】
export function delNvr(nvrId) {
  return request({
    url: '/screen/nvr/' + nvrId,
    method: 'delete'
  })
}
