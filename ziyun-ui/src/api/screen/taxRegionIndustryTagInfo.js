import request from '@/utils/request'

// 查询行业分类列表
export function listTaxRegionIndustryTagInfo(query) {
  return request({
    url: '/screen/taxRegionIndustryTagInfo/list',
    method: 'get',
    params: query
  })
}

// 查询行业分类详细
export function getTaxRegionIndustryTagInfo(regionId) {
  return request({
    url: '/screen/taxRegionIndustryTagInfo/' + regionId,
    method: 'get'
  })
}

// 新增行业分类
export function addTaxRegionIndustryTagInfo(data) {
  return request({
    url: '/screen/taxRegionIndustryTagInfo',
    method: 'post',
    data: data
  })
}

// 修改行业分类
export function updateTaxRegionIndustryTagInfo(data) {
  return request({
    url: '/screen/taxRegionIndustryTagInfo',
    method: 'put',
    data: data
  })
}

// 删除行业分类
export function delTaxRegionIndustryTagInfo(regionId) {
  return request({
    url: '/screen/taxRegionIndustryTagInfo/' + regionId,
    method: 'delete'
  })
}
