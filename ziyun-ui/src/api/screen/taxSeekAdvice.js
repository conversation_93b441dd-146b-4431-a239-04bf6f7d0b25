import request from '@/utils/request'

// 查询咨询热点列表
export function listTaxSeekAdvice(query) {
  return request({
    url: '/screen/taxSeekAdvice/list',
    method: 'get',
    params: query
  })
}

// 查询咨询热点详细
export function getTaxSeekAdvice(id) {
  return request({
    url: '/screen/taxSeekAdvice/' + id,
    method: 'get'
  })
}

// 新增咨询热点
export function addTaxSeekAdvice(data) {
  return request({
    url: '/screen/taxSeekAdvice',
    method: 'post',
    data: data
  })
}

// 修改咨询热点
export function updateTaxSeekAdvice(data) {
  return request({
    url: '/screen/taxSeekAdvice',
    method: 'put',
    data: data
  })
}

// 删除咨询热点
export function delTaxSeekAdvice(id) {
  return request({
    url: '/screen/taxSeekAdvice/' + id,
    method: 'delete'
  })
}
