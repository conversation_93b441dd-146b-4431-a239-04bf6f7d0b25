import request from '@/utils/request'

// 查询税收收入(年)列表
export function listTaxIncomeYear(query) {
  return request({
    url: '/screen/taxIncomeYear/list',
    method: 'get',
    params: query
  })
}

// 查询税收收入(年)详细
export function getTaxIncomeYear(id) {
  return request({
    url: '/screen/taxIncomeYear/' + id,
    method: 'get'
  })
}

// 新增税收收入(年)
export function addTaxIncomeYear(data) {
  return request({
    url: '/screen/taxIncomeYear',
    method: 'post',
    data: data
  })
}

// 修改税收收入(年)
export function updateTaxIncomeYear(data) {
  return request({
    url: '/screen/taxIncomeYear',
    method: 'put',
    data: data
  })
}

// 删除税收收入(年)
export function delTaxIncomeYear(id) {
  return request({
    url: '/screen/taxIncomeYear/' + id,
    method: 'delete'
  })
}
