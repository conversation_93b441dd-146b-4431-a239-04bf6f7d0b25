import request from '@/utils/request'

// 查询业务量总览列表
export function listTaxBizVol(query) {
  return request({
    url: '/screen/taxBizVol/list',
    method: 'get',
    params: query
  })
}

// 查询业务量总览详细
export function getTaxBizVol(id) {
  return request({
    url: '/screen/taxBizVol/' + id,
    method: 'get'
  })
}

// 新增业务量总览
export function addTaxBizVol(data) {
  return request({
    url: '/screen/taxBizVol',
    method: 'post',
    data: data
  })
}

// 修改业务量总览
export function updateTaxBizVol(data) {
  return request({
    url: '/screen/taxBizVol',
    method: 'put',
    data: data
  })
}

// 删除业务量总览
export function delTaxBizVol(id) {
  return request({
    url: '/screen/taxBizVol/' + id,
    method: 'delete'
  })
}
