import request from '@/utils/request'

// 查询三服务标签列表
export function listThreeSrvTagAnalysis(query) {
  return request({
    url: '/screen/threeSrvTagAnalysis/list',
    method: 'get',
    params: query
  })
}

// 查询三服务标签详细
export function getThreeSrvTagAnalysis(id) {
  return request({
    url: '/screen/threeSrvTagAnalysis/' + id,
    method: 'get'
  })
}

// 新增三服务标签
export function addThreeSrvTagAnalysis(data) {
  return request({
    url: '/screen/threeSrvTagAnalysis',
    method: 'post',
    data: data
  })
}

// 修改三服务标签
export function updateThreeSrvTagAnalysis(data) {
  return request({
    url: '/screen/threeSrvTagAnalysis',
    method: 'put',
    data: data
  })
}

// 删除三服务标签
export function delThreeSrvTagAnalysis(id) {
  return request({
    url: '/screen/threeSrvTagAnalysis/' + id,
    method: 'delete'
  })
}
