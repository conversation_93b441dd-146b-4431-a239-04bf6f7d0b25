import request from '@/utils/request'

// 查询电话热点咨询列表
export function listPhoneTagAnalysis(query) {
  return request({
    url: '/screen/phoneTagAnalysis/list',
    method: 'get',
    params: query
  })
}

// 查询电话热点咨询详细
export function getPhoneTagAnalysis(id) {
  return request({
    url: '/screen/phoneTagAnalysis/' + id,
    method: 'get'
  })
}

// 新增电话热点咨询
export function addPhoneTagAnalysis(data) {
  return request({
    url: '/screen/phoneTagAnalysis',
    method: 'post',
    data: data
  })
}

// 修改电话热点咨询
export function updatePhoneTagAnalysis(data) {
  return request({
    url: '/screen/phoneTagAnalysis',
    method: 'put',
    data: data
  })
}

// 删除电话热点咨询
export function delPhoneTagAnalysis(id) {
  return request({
    url: '/screen/phoneTagAnalysis/' + id,
    method: 'delete'
  })
}
