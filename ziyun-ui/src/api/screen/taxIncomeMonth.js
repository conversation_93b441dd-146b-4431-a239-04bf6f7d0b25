import request from '@/utils/request'

// 查询税收收入(月)列表
export function listTaxIncomeMonth(query) {
  return request({
    url: '/screen/taxIncomeMonth/list',
    method: 'get',
    params: query
  })
}

// 查询税收收入(月)详细
export function getTaxIncomeMonth(id) {
  return request({
    url: '/screen/taxIncomeMonth/' + id,
    method: 'get'
  })
}

// 新增税收收入(月)
export function addTaxIncomeMonth(data) {
  return request({
    url: '/screen/taxIncomeMonth',
    method: 'post',
    data: data
  })
}

// 修改税收收入(月)
export function updateTaxIncomeMonth(data) {
  return request({
    url: '/screen/taxIncomeMonth',
    method: 'put',
    data: data
  })
}

// 删除税收收入(月)
export function delTaxIncomeMonth(id) {
  return request({
    url: '/screen/taxIncomeMonth/' + id,
    method: 'delete'
  })
}
