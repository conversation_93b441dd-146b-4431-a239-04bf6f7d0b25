<template>
  <el-image
    :src="`${realSrc}`"
    fit="cover"
    :style="`width:${realWidth};height:${realHeight};`"
    :preview-src-list="realSrcList"
  >
    <div slot="error" class="image-slot">
      <i class="el-icon-picture-outline"></i>
    </div>
  </el-image>
</template>

<script>
import { listByIds } from "@/api/system/oss";

export default {
  name: "ImagePreview",
  props: {
    src: {
      type: String,
      default: ""
    },
    value: {
      type: [String, Array],
      default: ""
    },
    width: {
      type: [Number, String],
      default: ""
    },
    height: {
      type: [Number, String],
      default: ""
    }
  },
  data() {
    return {
      imageUrl: "",
      imageList: []
    };
  },
  watch: {
    value: {
      handler(val) {
        this.handleImageUrl(val);
      },
      immediate: true
    },
    src: {
      handler(val) {
        this.handleImageUrl(val);
      },
      immediate: true
    }
  },
  methods: {
    async handleImageUrl(val) {
      if (val) {
        let list;
        if (Array.isArray(val)) {
          list = val;
        } else if (val.startsWith('http://') || val.startsWith('https://')) {
          // 如果是直接的 URL，直接使用
          this.imageUrl = val;
          this.imageList = [val];
          return;
        } else {
          // 否则尝试通过 OSS ID 获取
          try {
            const res = await listByIds(val);
            list = res.data;
          } catch (error) {
            console.error('Failed to get image URL:', error);
            return;
          }
        }
        if (list && list.length > 0) {
          this.imageUrl = list[0].url;
          this.imageList = list.map(item => item.url);
        }
      }
    }
  },
  computed: {
    realSrc() {
      return this.imageUrl;
    },
    realSrcList() {
      return this.imageList;
    },
    realWidth() {
      return typeof this.width == "string" ? this.width : `${this.width}px`;
    },
    realHeight() {
      return typeof this.height == "string" ? this.height : `${this.height}px`;
    }
  }
};
</script>

<style lang="scss" scoped>
.el-image {
  border-radius: 5px;
  background-color: #ebeef5;
  box-shadow: 0 0 5px 1px #ccc;
  ::v-deep .el-image__inner {
    transition: all 0.3s;
    cursor: pointer;
    &:hover {
      transform: scale(1.2);
    }
  }
  ::v-deep .image-slot {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    color: #909399;
    font-size: 30px;
  }
}
</style>
