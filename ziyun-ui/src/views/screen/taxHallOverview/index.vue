<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['screen:taxHallOverview:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['screen:taxHallOverview:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['screen:taxHallOverview:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['screen:taxHallOverview:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="taxHallOverviewList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="id" align="center" prop="id" v-if="false"/>
      <el-table-column label="税局简介" align="center" prop="taxHallInfo" :show-overflow-tooltip="true"/>
      <el-table-column label="相关图片" align="center" prop="infoImageUrl" :show-overflow-tooltip="true"/>
      <el-table-column label="纳税人" align="center" prop="taxpayers" />
      <el-table-column label="单位纳税人" align="center" prop="establishmentsTaxpayers" />
      <el-table-column label="一般纳税人" align="center" prop="generalTaxpayers" />
      <el-table-column label="缴费人" align="center" prop="payers" />
      <el-table-column label="个人经营纳税人" align="center" prop="personalBusinessTaxpayers" />
      <el-table-column label="小规模纳税人" align="center" prop="smallScaleTaxpayers" />
      <el-table-column label="同比(单位:%)" align="center" prop="increaseRate" />
      <el-table-column label="年度收入" align="center" prop="allYearIncome" />
      <el-table-column label="序号" align="center" prop="unitCode" v-if="false"/>
      <el-table-column label="税务机关代码" align="center" prop="swjgdm" v-if="false"/>
      <el-table-column label="用户ID" align="center" prop="userId" v-if="false"/>
      <el-table-column label="部门ID" align="center" prop="deptId" v-if="false"/>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['screen:taxHallOverview:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['screen:taxHallOverview:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改税局信息总览对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="150px">
        <el-form-item label="税局简介" prop="taxHallInfo">
          <el-input v-model="form.taxHallInfo" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="相关图片" prop="infoImageUrl">
          <el-input v-model="form.infoImageUrl" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="纳税人" prop="taxpayers">
          <el-input v-model="form.taxpayers" placeholder="请输入纳税人" />
        </el-form-item>
        <el-form-item label="单位纳税人" prop="establishmentsTaxpayers">
          <el-input v-model="form.establishmentsTaxpayers" placeholder="请输入单位纳税人" />
        </el-form-item>
        <el-form-item label="一般纳税人" prop="generalTaxpayers">
          <el-input v-model="form.generalTaxpayers" placeholder="请输入一般纳税人" />
        </el-form-item>
        <el-form-item label="缴费人" prop="payers">
          <el-input v-model="form.payers" placeholder="请输入缴费人" />
        </el-form-item>
        <el-form-item label="个人经营纳税人" prop="personalBusinessTaxpayers">
          <el-input v-model="form.personalBusinessTaxpayers" placeholder="请输入个人经营纳税人" />
        </el-form-item>
        <el-form-item label="小规模纳税人" prop="smallScaleTaxpayers">
          <el-input v-model="form.smallScaleTaxpayers" placeholder="请输入小规模纳税人" />
        </el-form-item>
        <el-form-item label="同比(单位:%)" prop="increaseRate">
          <el-input v-model="form.increaseRate" placeholder="请输入同比(单位:%)" />
        </el-form-item>
        <el-form-item label="年度收入" prop="allYearIncome">
          <el-input v-model="form.allYearIncome" placeholder="请输入年度收入" />
        </el-form-item>
        <el-form-item label="序号" prop="unitCode">
          <el-input v-model="form.unitCode" placeholder="请输入序号" />
        </el-form-item>
        <el-form-item label="税务机关代码" prop="swjgdm">
          <el-input v-model="form.swjgdm" placeholder="请输入税务机关代码" />
        </el-form-item>
        <el-form-item prop="userId" v-model="form.userId" v-if="false"/>
        <el-form-item prop="deptId" v-model="form.deptId" v-if="false"/>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listTaxHallOverview, getTaxHallOverview, delTaxHallOverview, addTaxHallOverview, updateTaxHallOverview } from "@/api/screen/taxHallOverview";

import {getInfo} from "@/api/login";

export default {
  name: "TaxHallOverview",
  data() {
    return {
      // 按钮loading
      buttonLoading: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 税局信息总览表格数据
      taxHallOverviewList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        id: undefined,
        taxHallInfo: undefined,
        infoImageUrl: undefined,
        taxpayers: undefined,
        establishmentsTaxpayers: undefined,
        generalTaxpayers: undefined,
        payers: undefined,
        personalBusinessTaxpayers: undefined,
        smallScaleTaxpayers: undefined,
        increaseRate: undefined,
        allYearIncome: undefined,
        unitCode: undefined,
        swjgdm: undefined,
        userId: undefined,
        deptId: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        id: [
          { required: true, message: "不能为空", trigger: "blur" }
        ],
        taxHallInfo: [
          { required: true, message: "税局简介不能为空", trigger: "blur" }
        ],
        infoImageUrl: [
          { required: true, message: "相关图片不能为空", trigger: "blur" }
        ],
        taxpayers: [
          { required: true, message: "纳税人不能为空", trigger: "blur" }
        ],
        establishmentsTaxpayers: [
          { required: true, message: "单位纳税人不能为空", trigger: "blur" }
        ],
        generalTaxpayers: [
          { required: true, message: "一般纳税人不能为空", trigger: "blur" }
        ],
        payers: [
          { required: true, message: "缴费人不能为空", trigger: "blur" }
        ],
        personalBusinessTaxpayers: [
          { required: true, message: "个人经营纳税人不能为空", trigger: "blur" }
        ],
        smallScaleTaxpayers: [
          { required: true, message: "小规模纳税人不能为空", trigger: "blur" }
        ],
        increaseRate: [
          { required: true, message: "同比(单位:%)不能为空", trigger: "blur" }
        ],
        allYearIncome: [
          { required: true, message: "年度收入不能为空", trigger: "blur" }
        ],
        unitCode: [
          { required: true, message: "序号不能为空", trigger: "blur" }
        ],
        swjgdm: [
          { required: true, message: "税务机关代码不能为空", trigger: "blur" }
        ],
        userId: [
          { required: true, message: "用户ID不能为空", trigger: "blur" }
        ],
        deptId: [
          { required: true, message: "部门ID不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询税局信息总览列表 */
    getList() {
      this.loading = true;
      listTaxHallOverview(this.queryParams).then(response => {
        this.taxHallOverviewList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        taxHallInfo: undefined,
        infoImageUrl: undefined,
        taxpayers: undefined,
        establishmentsTaxpayers: undefined,
        generalTaxpayers: undefined,
        payers: undefined,
        personalBusinessTaxpayers: undefined,
        smallScaleTaxpayers: undefined,
        increaseRate: undefined,
        allYearIncome: undefined,
        unitCode: undefined,
        swjgdm: undefined,
        delFlag: undefined,
        createBy: undefined,
        createTime: undefined,
        updateBy: undefined,
        updateTime: undefined,
        userId: undefined,
        deptId: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      getInfo().then(response => {
        this.form.userId = response.data.user.userId;
        this.form.deptId = response.data.user.deptId;
      });
      this.title = "添加税局信息总览";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.loading = true;
      this.reset();
      const id = row.id || this.ids
      getTaxHallOverview(id).then(response => {
        this.loading = false;
        this.form = response.data;
        this.open = true;
        this.title = "修改税局信息总览";
      });
      getInfo().then(response => {
        this.form.userId = response.data.user.userId;
        this.form.deptId = response.data.user.deptId;
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.buttonLoading = true;
          if (this.form.id != null) {
            updateTaxHallOverview(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          } else {
            addTaxHallOverview(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除税局信息总览编号为"' + ids + '"的数据项？').then(() => {
        this.loading = true;
        return delTaxHallOverview(ids);
      }).then(() => {
        this.loading = false;
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      }).finally(() => {
        this.loading = false;
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('screen/taxHallOverview/export', {
        ...this.queryParams
      }, `taxHallOverview_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
