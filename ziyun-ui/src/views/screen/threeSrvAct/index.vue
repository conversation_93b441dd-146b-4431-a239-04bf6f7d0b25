<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['screen:threeSrvAct:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['screen:threeSrvAct:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['screen:threeSrvAct:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['screen:threeSrvAct:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="threeSrvActList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" v-if="false"/>
      <el-table-column label="活动集锦内容" align="center" prop="actContent" >
        <template slot-scope="scope">
          <div v-html="scope.row.actContent"></div>
        </template>
      </el-table-column>
      <el-table-column label="活动图片精选" align="center" prop="actImage" width="100">
        <template slot-scope="scope">
          <div v-if="scope.row.actImage" class="image-preview">
            <image-preview :value="scope.row.actImage"></image-preview>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="用户ID" align="center" prop="userId" v-if="false" />
      <el-table-column label="部门ID" align="center" prop="deptId" v-if="false" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['screen:threeSrvAct:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['screen:threeSrvAct:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改三服务活动集锦对话框 -->
    <el-dialog 
      :title="title" 
      :visible.sync="open" 
      width="800px" 
      append-to-body
      :close-on-click-modal="false"
      :destroy-on-close="true">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="活动集锦内容" prop="actContent">
          <editor 
            v-model="form.actContent" 
            :min-height="250"
            :init="{
              language: 'zh_CN',
              plugins: ['advlist anchor autolink autosave code codesample colorpicker colorpicker contextmenu directionality emoticons fullscreen hr image imagetools insertdatetime link lists media nonbreaking noneditable pagebreak paste preview print save searchreplace spellchecker tabfocus table template textcolor textpattern visualblocks visualchars wordcount'],
              toolbar: ['searchreplace bold italic underline strikethrough alignleft aligncenter alignright outdent indent blockquote undo redo removeformat subscript superscript code codesample', 'hr bullist numlist link image charmap preview anchor pagebreak insertdatetime media table emoticons forecolor backcolor fullscreen'],
              height: 400
            }"
          />
        </el-form-item>
        <el-form-item label="活动图片精选" prop="actImage">
          <image-upload
            v-model="form.actImage"
            :limit="5"
            :fileSize="5"
            :fileType="['png', 'jpg', 'jpeg']"
            :isShowTip="true"
          >
            <template slot="tip">
              <div class="el-upload__tip">
                支持上传PNG、JPG、JPEG格式图片，单个文件不超过5MB，最多上传5张
              </div>
            </template>
          </image-upload>
        </el-form-item>
        <el-form-item prop="userId" v-model="form.userId" v-if="false"/>
        <el-form-item prop="deptId" v-model="form.deptId" v-if="false"/>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listThreeSrvAct, getThreeSrvAct, delThreeSrvAct, addThreeSrvAct, updateThreeSrvAct } from "@/api/screen/threeSrvAct";
import ImageUpload from "@/components/ImageUpload";
import ImagePreview from "@/components/ImagePreview";
import {getInfo} from "@/api/login";

export default {
  components: { ImageUpload, ImagePreview },
  name: "ThreeSrvAct",
  data() {
    return {
      // 按钮loading
      buttonLoading: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 三服务活动集锦表格数据
      threeSrvActList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 用户信息
      userInfo: null,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        actContent: undefined,
        actImage: undefined,
        userId: undefined,
        deptId: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        actContent: [
          { required: true, message: "活动集锦内容不能为空", trigger: "blur" }
        ],
        actImage: [
          { required: true, message: "活动图片精选不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    // 先获取用户信息，再获取列表
    getInfo().then(response => {
      if (response && response.data && response.data.user) {
        this.userInfo = response.data.user;
        this.getList();
      } else {
        this.$modal.msgError("获取用户信息失败");
      }
    }).catch(error => {
      this.$modal.msgError("获取用户信息失败：" + error);
    });
  },
  methods: {
    /** 查询三服务活动集锦列表 */
    getList() {
      this.loading = true;
      listThreeSrvAct(this.queryParams).then(response => {
        console.log('活动列表数据：', response.rows);
        console.log('第一条数据的actImage：', response.rows[0]?.actImage);
        this.threeSrvActList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        actContent: undefined,
        actImage: undefined,
        userId: undefined,
        deptId: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      if (this.userInfo) {
        this.form.userId = this.userInfo.userId;
        this.form.deptId = this.userInfo.deptId;
        this.open = true;
        this.title = "添加三服务活动集锦";
      } else {
        this.$modal.msgError("获取用户信息失败，请刷新页面重试");
      }
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.loading = true;
      this.reset();
      const id = row.id || this.ids
      getThreeSrvAct(id).then(response => {
        this.loading = false;
        this.form = response.data;
        this.open = true;
        this.title = "修改三服务活动集锦";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.buttonLoading = true;
          if (this.form.id != null) {
            updateThreeSrvAct(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          } else {
            addThreeSrvAct(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids.join(',');
      this.$modal.confirm('是否确认删除三服务活动集锦编号为"' + ids + '"的数据项？').then(() => {
        this.loading = true;
        return delThreeSrvAct(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {}).finally(() => {
        this.loading = false;
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('screen/threeSrvAct/export', {
        ...this.queryParams
      }, `threeSrvAct_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
