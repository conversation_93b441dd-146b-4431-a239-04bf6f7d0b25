<template>
  <div class="app-container">
<!--    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">-->
<!--      <el-form-item label="智慧办税服务" prop="indexName">-->
<!--        <el-input-->
<!--          v-model="queryParams.indexName"-->
<!--          placeholder="请输入智慧办税服务"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="感知指数" prop="indexValue">-->
<!--        <el-input-->
<!--          v-model="queryParams.indexValue"-->
<!--          placeholder="请输入感知指数"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="指数左偏移" prop="indexLeft">-->
<!--        <el-input-->
<!--          v-model="queryParams.indexLeft"-->
<!--          placeholder="请输入指数左偏移"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="指数顶部偏移" prop="idnexTop">-->
<!--        <el-input-->
<!--          v-model="queryParams.idnexTop"-->
<!--          placeholder="请输入指数顶部偏移"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item>-->
<!--        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>-->
<!--        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>-->
<!--      </el-form-item>-->
<!--    </el-form>-->

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['screen:taxSmartServiceIndex:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['screen:taxSmartServiceIndex:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['screen:taxSmartServiceIndex:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['screen:taxSmartServiceIndex:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="taxSmartServiceIndexList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="id" align="center" prop="id" v-if="false"/>
      <el-table-column label="智慧办税服务" align="center" prop="indexName" />
      <el-table-column label="感知指数" align="center" prop="indexValue" />
      <el-table-column label="指数左偏移" align="center" prop="indexLeft" />
      <el-table-column label="指数顶部偏移" align="center" prop="idnexTop" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['screen:taxSmartServiceIndex:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['screen:taxSmartServiceIndex:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改智慧办税服务指数对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="智慧办税服务" prop="indexName">
          <el-input v-model="form.indexName" placeholder="请输入智慧办税服务" />
        </el-form-item>
        <el-form-item label="感知指数" prop="indexValue">
          <el-input v-model="form.indexValue" placeholder="请输入感知指数" />
        </el-form-item>
        <el-form-item label="指数左偏移" prop="indexLeft">
          <el-input v-model="form.indexLeft" placeholder="请输入指数左偏移" />
        </el-form-item>
        <el-form-item label="指数顶部偏移" prop="idnexTop">
          <el-input v-model="form.idnexTop" placeholder="请输入指数顶部偏移" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listTaxSmartServiceIndex, getTaxSmartServiceIndex, delTaxSmartServiceIndex, addTaxSmartServiceIndex, updateTaxSmartServiceIndex } from "@/api/screen/taxSmartServiceIndex";

import {getInfo} from "@/api/login";

export default {
  name: "TaxSmartServiceIndex",
  data() {
    return {
      // 按钮loading
      buttonLoading: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 智慧办税服务指数表格数据
      taxSmartServiceIndexList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        indexName: undefined,
        indexValue: undefined,
        indexLeft: undefined,
        idnexTop: undefined,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        id: [
          { required: true, message: "id不能为空", trigger: "blur" }
        ],
        indexName: [
          { required: true, message: "智慧办税服务不能为空", trigger: "blur" }
        ],
        indexValue: [
          { required: true, message: "感知指数不能为空", trigger: "blur" }
        ],
        indexLeft: [
          { required: true, message: "指数左偏移不能为空", trigger: "blur" }
        ],
        idnexTop: [
          { required: true, message: "指数顶部偏移不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询智慧办税服务指数列表 */
    getList() {
      this.loading = true;
      listTaxSmartServiceIndex(this.queryParams).then(response => {
        this.taxSmartServiceIndexList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        indexName: undefined,
        indexValue: undefined,
        indexLeft: undefined,
        idnexTop: undefined,
        createBy: undefined,
        createTime: undefined,
        updateBy: undefined,
        updateTime: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      getInfo().then(response => {
        this.form.userId = response.data.user.userId;
        this.form.deptId = response.data.user.deptId;
      });
      this.title = "添加智慧办税服务指数";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.loading = true;
      this.reset();
      const id = row.id || this.ids
      getTaxSmartServiceIndex(id).then(response => {
        this.loading = false;
        this.form = response.data;
        this.open = true;
        this.title = "修改智慧办税服务指数";
      });
      getInfo().then(response => {
        this.form.userId = response.data.user.userId;
        this.form.deptId = response.data.user.deptId;
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.buttonLoading = true;
          if (this.form.id != null) {
            updateTaxSmartServiceIndex(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          } else {
            addTaxSmartServiceIndex(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除智慧办税服务指数编号为"' + ids + '"的数据项？').then(() => {
        this.loading = true;
        return delTaxSmartServiceIndex(ids);
      }).then(() => {
        this.loading = false;
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      }).finally(() => {
        this.loading = false;
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('screen/taxSmartServiceIndex/export', {
        ...this.queryParams
      }, `taxSmartServiceIndex_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
