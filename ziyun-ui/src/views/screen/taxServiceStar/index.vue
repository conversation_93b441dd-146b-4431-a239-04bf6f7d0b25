<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-card class="search-wrapper" shadow="never">
      <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="84px" size="small">
        <el-form-item label="服务大厅" prop="unitCode">
          <el-select
            v-model="queryParams.unitCode"
            placeholder="请选择服务大厅"
            clearable
            style="width: 240px"
          >
            <el-option
              v-for="dict in dict.type.tax_hall_name"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="服务之星" prop="starName">
          <el-input
            v-model="queryParams.starName"
            placeholder="请输入服务之星姓名"
            clearable
            style="width: 240px"
            @keyup.enter.native="handleQuery"
          >
            <i slot="prefix" class="el-input__icon el-icon-search"></i>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮区域 -->
    <el-row :gutter="10" class="mb8 operation-wrapper">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['screen:taxServiceStar:add']"
        >新增服务之星</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['screen:taxServiceStar:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['screen:taxServiceStar:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['screen:taxServiceStar:export']"
        >导出</el-button>
      </el-col>
    </el-row>

    <!-- 数据列表区域 -->
    <el-card shadow="never" class="table-wrapper">
      <el-table
        v-loading="loading"
        :data="taxServiceStarList"
        @selection-change="handleSelectionChange"
        :header-cell-style="{ background: '#f5f7fa' }"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="服务大厅" align="center" min-width="120">
          <template slot-scope="scope">
            <el-tag size="medium">
              <dict-tag :options="dict.type.tax_hall_name" :value="scope.row.unitCode"/>
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="服务之星" align="center" min-width="120" prop="starName" />
        <el-table-column label="照片" align="center" width="100">
          <template slot-scope="scope">
            <el-popover
              placement="right"
              trigger="hover"
              popper-class="image-preview-popover"
            >
              <image-preview :src="scope.row.starImageurl" :width="200" :height="200"/>
              <image-preview slot="reference" :src="scope.row.starImageurl" :width="50" :height="50"/>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column label="详情" align="center" min-width="300" show-overflow-tooltip>
          <template slot-scope="scope">
            <div class="content-wrapper" v-html="scope.row.starContent"></div>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="180">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['screen:taxServiceStar:edit']"
            >修改</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['screen:taxServiceStar:remove']"
              class="delete-btn"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 添加或修改对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="700px"
      append-to-body
      :close-on-click-modal="false"
      class="service-star-dialog"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="服务大厅" prop="unitCode">
          <el-select v-model="form.unitCode" placeholder="请选择服务大厅" style="width: 100%">
            <el-option
              v-for="dict in dict.type.tax_hall_name"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="服务之星" prop="starName">
          <el-input v-model="form.starName" placeholder="请输入服务之星姓名" />
        </el-form-item>
        <el-form-item label="照片" prop="starImageurl">
          <image-upload v-model="form.starImageurl"/>
        </el-form-item>
        <el-form-item label="详细介绍">
          <editor v-model="form.starContent" :min-height="192"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listTaxServiceStar, getTaxServiceStar, delTaxServiceStar, addTaxServiceStar, updateTaxServiceStar } from "@/api/screen/taxServiceStar";

export default {
  name: "TaxServiceStar",
  dicts: ['tax_hall_name'],
  data() {
    return {
      // 按钮loading
      buttonLoading: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 纳税服务之星表格数据
      taxServiceStarList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        unitCode: undefined,
        starName: undefined,
        starImageurl: undefined,
        starContent: undefined,
        userId: undefined,
        deptId: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        unitCode: [
          { required: true, message: "服务大厅不能为空", trigger: "change" }
        ],
        starName: [
          { required: true, message: "服务之星不能为空", trigger: "blur" }
        ],
        starImageurl: [
          { required: true, message: "照片不能为空", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询纳税服务之星列表 */
    getList() {
      this.loading = true;
      listTaxServiceStar(this.queryParams).then(response => {
        this.taxServiceStarList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        unitCode: undefined,
        starName: undefined,
        starImageurl: undefined,
        starContent: undefined,
        userId: undefined,
        deptId: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加服务之星";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.loading = true;
      this.reset();
      const id = row.id || this.ids
      getTaxServiceStar(id).then(response => {
        this.loading = false;
        this.form = response.data;
        this.open = true;
        this.title = "修改服务之星";
      });
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.buttonLoading = true;
          if (this.form.id != undefined) {
            updateTaxServiceStar(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          } else {
            addTaxServiceStar(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除服务之星编号为"' + ids + '"的数据项？').then(() => {
        this.loading = true;
        return delTaxServiceStar(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).finally(() => {
        this.loading = false;
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('screen/taxServiceStar/export', {
        ...this.queryParams
      }, `taxServiceStar_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f0f2f5;

  .search-wrapper {
    margin-bottom: 20px;
    .el-form {
      margin-bottom: -18px;
    }
  }

  .operation-wrapper {
    margin: 20px 0;
  }

  .table-wrapper {
    .el-table {
      margin: 15px 0;
    }
  }
}

.content-wrapper {
  max-height: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  line-height: 1.5;
  text-align: left;
  padding: 0 10px;
}

.delete-btn {
  color: #f56c6c;
  margin-left: 10px;
  &:hover {
    color: #ff4d4d;
  }
}

.service-star-dialog {
  .el-dialog__body {
    padding: 20px 40px;
  }
}

:deep(.image-preview-popover) {
  padding: 10px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style>
