<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="时间维度(单位:月)" prop="anaylsisTime">
        <el-date-picker clearable
          v-model="queryParams.anaylsisTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择时间维度(单位:月)">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="咨询热点事项" prop="hotName">
        <el-input
          v-model="queryParams.hotName"
          placeholder="请输入咨询热点事项"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="咨询量" prop="hotVol">
        <el-input
          v-model="queryParams.hotVol"
          placeholder="请输入咨询量"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="咨询占比" prop="hotRate">
        <el-input
          v-model="queryParams.hotRate"
          placeholder="请输入咨询占比"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
<!--      <el-form-item label="用户ID" prop="userId">-->
<!--        <el-input-->
<!--          v-model="queryParams.userId"-->
<!--          placeholder="请输入用户ID"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="部门ID" prop="deptId">-->
<!--        <el-input-->
<!--          v-model="queryParams.deptId"-->
<!--          placeholder="请输入部门ID"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['screen:phoneTagAnalysis:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['screen:phoneTagAnalysis:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['screen:phoneTagAnalysis:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['screen:phoneTagAnalysis:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="phoneTagAnalysisList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" v-if="false"/>
      <el-table-column label="时间维度(单位:月)" align="center" prop="anaylsisTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.anaylsisTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="咨询热点事项" align="center" prop="hotName" />
      <el-table-column label="咨询量" align="center" prop="hotVol" />
      <el-table-column label="咨询占比" align="center" prop="hotRate" />
      <el-table-column label="用户ID" align="center" prop="userId" v-if="false" />
      <el-table-column label="部门ID" align="center" prop="deptId" v-if="false" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['screen:phoneTagAnalysis:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['screen:phoneTagAnalysis:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改电话热点咨询对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="时间维度(单位:月)" prop="anaylsisTime">
          <el-date-picker clearable
            v-model="form.anaylsisTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="请选择时间维度(单位:月)">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="咨询热点事项" prop="hotName">
          <el-input v-model="form.hotName" placeholder="请输入咨询热点事项" />
        </el-form-item>
        <el-form-item label="咨询量" prop="hotVol">
          <el-input v-model="form.hotVol" placeholder="请输入咨询量" />
        </el-form-item>
        <el-form-item label="咨询占比" prop="hotRate">
          <el-input v-model="form.hotRate" placeholder="请输入咨询占比" />
        </el-form-item>
        <el-form-item prop="userId" v-model="form.userId" v-if="false"/>
        <el-form-item prop="deptId" v-model="form.deptId" v-if="false"/>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listPhoneTagAnalysis, getPhoneTagAnalysis, delPhoneTagAnalysis, addPhoneTagAnalysis, updatePhoneTagAnalysis } from "@/api/screen/phoneTagAnalysis";

import {getInfo} from "@/api/login";

export default {
  name: "PhoneTagAnalysis",
  data() {
    return {
      // 按钮loading
      buttonLoading: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 电话热点咨询表格数据
      phoneTagAnalysisList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        anaylsisTime: undefined,
        hotName: undefined,
        hotVol: undefined,
        hotRate: undefined,
        userId: undefined,
        deptId: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        id: [
          { required: true, message: "ID不能为空", trigger: "blur" }
        ],
        anaylsisTime: [
          { required: true, message: "时间维度(单位:月)不能为空", trigger: "blur" }
        ],
        hotName: [
          { required: true, message: "咨询热点事项不能为空", trigger: "blur" }
        ],
        hotVol: [
          { required: true, message: "咨询量不能为空", trigger: "blur" }
        ],
        hotRate: [
          { required: true, message: "咨询占比不能为空", trigger: "blur" }
        ],
        userId: [
          { required: true, message: "用户ID不能为空", trigger: "blur" }
        ],
        deptId: [
          { required: true, message: "部门ID不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询电话热点咨询列表 */
    getList() {
      this.loading = true;
      listPhoneTagAnalysis(this.queryParams).then(response => {
        this.phoneTagAnalysisList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        anaylsisTime: undefined,
        hotName: undefined,
        hotVol: undefined,
        hotRate: undefined,
        createBy: undefined,
        createTime: undefined,
        updateBy: undefined,
        updateTime: undefined,
        userId: undefined,
        deptId: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      getInfo().then(response => {
        this.form.userId = response.data.user.userId;
        this.form.deptId = response.data.user.deptId;
      });
      this.title = "添加电话热点咨询";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.loading = true;
      this.reset();
      const id = row.id || this.ids
      getPhoneTagAnalysis(id).then(response => {
        this.loading = false;
        this.form = response.data;
        this.open = true;
        this.title = "修改电话热点咨询";
      });
      getInfo().then(response => {
        this.form.userId = response.data.user.userId;
        this.form.deptId = response.data.user.deptId;
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.buttonLoading = true;
          if (this.form.id != null) {
            updatePhoneTagAnalysis(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          } else {
            addPhoneTagAnalysis(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除电话热点咨询编号为"' + ids + '"的数据项？').then(() => {
        this.loading = true;
        return delPhoneTagAnalysis(ids);
      }).then(() => {
        this.loading = false;
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      }).finally(() => {
        this.loading = false;
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('screen/phoneTagAnalysis/export', {
        ...this.queryParams
      }, `phoneTagAnalysis_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
