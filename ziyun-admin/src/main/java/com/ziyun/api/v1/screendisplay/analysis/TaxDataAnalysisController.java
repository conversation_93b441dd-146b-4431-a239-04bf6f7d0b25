package com.ziyun.api.v1.screendisplay.analysis;

import cn.hutool.http.HtmlUtil;
import com.ziyun.common.core.domain.R;
import com.ziyun.common.utils.DateUtils;
import com.ziyun.screen.domain.bo.*;
import com.ziyun.screen.domain.chart.AreaStyle;
import com.ziyun.screen.domain.chart.PathCharts;
import com.ziyun.screen.domain.chart.RadarCharts;
import com.ziyun.screen.domain.chart.RadarChartsData;
import com.ziyun.screen.domain.vo.*;
import com.ziyun.screen.service.*;
import com.ziyun.system.domain.vo.SysOssVo;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ziyun.oss.core.OssClient;
import com.ziyun.oss.factory.OssFactory;
import java.util.stream.Collectors;
import com.ziyun.system.service.ISysOssService;
import com.ziyun.system.domain.SysOss;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

@RequiredArgsConstructor(onConstructor_ = @Autowired)
@CrossOrigin
@RestController
@RequestMapping("/v1/screen-display/analysis")
public class TaxDataAnalysisController {
    private final IQscOverviewDataService overviewDataService;
    private final IQscPhoneAnalysisService phoneAnalysisService;
    private final IQscPhoneVolRateAnalysisService phoneVolRateAnalysisService;
    private final IQscPhoneTagAnalysisService tagAnalysisService;
    private final IQscTaxCostSysInfoService costSysInfoService;
    private final IQscTaxCostDealTagService costDealTagService;
    private final IQscThreeSrvTagAnalysisService threeSrvTagAnalysisService;
    private final IQscThreeSrvActService threeSrvActService;
    private final IQscTaxRankRateService rankRateService;
    private final IQscTaxRankChannelService rankChannelService;
    private final IQscTaxRankTagService tagService;
    private final IQscTaxCommDataService commDataService;
    private final TaxHallScreenService hallScreenService;
    private final IQscTaxSentimentMonthlyReportService reportService;
    private final ISysOssService ossService;

    /**
     * 税局数据总览列表
     */
//    @ApiOperation("税局数据总览")
    @GetMapping("/data-overview")
    @ResponseBody
    public R<List<QscOverviewDataVo>> dataOverview() {
        QscOverviewDataBo overviewDataBo = new QscOverviewDataBo();
        return R.ok(overviewDataService.queryList(overviewDataBo));
    }

    /**
     * 税局电话分析
     */
//    @ApiOperation("来电情况分析")
    @GetMapping("/phone")
    @ResponseBody
    public R<QscPhoneAnalysisVo> phone() {
        return R.ok(phoneAnalysisService.queryById(1L));
    }

    /**
     * 税局接听量和接听率
     */
//    @ApiOperation("接听量和接听率")
    @GetMapping("/phone-vol-rate")
    @ResponseBody
    public R<Map<String,Object>> phoneVolRate() {
        Map<String,Object> phoneVolRate = new HashMap<>();
        QscPhoneVolRateAnalysisBo volRateAnalysisBo = new QscPhoneVolRateAnalysisBo();
        List<String>  xAxis = new ArrayList<>();
        List<Long>  barY = new ArrayList<>();
        List<Long>  lineY = new ArrayList<>();

        List<QscPhoneVolRateAnalysisVo> volRateVos = phoneVolRateAnalysisService.queryList(volRateAnalysisBo);
        if (null != volRateVos){
            for (QscPhoneVolRateAnalysisVo volRate : volRateVos) {
                xAxis.add(DateUtils.parseDateToStr("MM",volRate.getAxisX()));
                barY.add(volRate.getBarY());
                lineY.add(volRate.getLineY());
            }
            phoneVolRate.put("xAxis",xAxis);
            phoneVolRate.put("barY",barY);
            phoneVolRate.put("lineY",lineY);
        }
        return R.ok(phoneVolRate);
    }

    /**
     * 咨询热线标签
     */
//    @ApiOperation("咨询热线标签")
    @GetMapping("/phone-tag")
    @ResponseBody
    public R<List<QscPhoneTagAnalysisVo>> phoneTag() {
        QscPhoneTagAnalysisBo tagBO = new QscPhoneTagAnalysisBo();
        List<QscPhoneTagAnalysisVo> tagAnalysisVoList = tagAnalysisService.queryList(tagBO);

        return R.ok(tagAnalysisVoList);
    }

    /**
     * 税费协同系统基础信息
     */
//    @ApiOperation("税费协同系统基础信息")
    @GetMapping("/cost-sys-info")
    @ResponseBody
    public R<QscTaxCostSysInfoVo> costSysInfo() {
        return R.ok(costSysInfoService.queryById(1L));
    }

    /**
     * 费协同工单
     * 税费协同标签名改推送种类
     */
//    @ApiOperation("推送种类")
    @GetMapping("/cost-deal-tag")
    @ResponseBody
    public R<List<QscTaxCostDealTagVo>> costDealTag() {
        QscTaxCostDealTagBo costDealTagBo = new QscTaxCostDealTagBo();
        List<QscTaxCostDealTagVo> costDealTagVos = costDealTagService.queryList(costDealTagBo);
        return R.ok(costDealTagVos);
    }

    /**
     * 三服务标签数据
     */
//    @ApiOperation("三服务标签")
    @GetMapping("/three-srv-tag")
    @ResponseBody
    public R<Map<String,Object>> threeSrvTag() {
        Map<String,Object> threeSrvTagPie = new HashMap<>();
        QscThreeSrvTagAnalysisBo threeSrvTagAnalysisBo = new QscThreeSrvTagAnalysisBo();
        List<QscThreeSrvTagAnalysisVo> threeSrvTagAnalysisVos = threeSrvTagAnalysisService.queryList(threeSrvTagAnalysisBo);
        List<Map> inPie = new ArrayList<>();
        List<Map> outPie = new ArrayList<>();

        if (null != threeSrvTagAnalysisVos){
            for (QscThreeSrvTagAnalysisVo threeSrvTag : threeSrvTagAnalysisVos) {
                Map<String,Object> inside = new HashMap<>();
                Map<String,Object> outside = new HashMap<>();
                if(threeSrvTag.getPieTag().equals("0")){
                    inside.put("value",Integer.valueOf(threeSrvTag.getSrvTagRate()));
                    inside.put("name",threeSrvTag.getSrvTag());
                    inPie.add(inside);
                }else {
                    outside.put("value",threeSrvTag.getSrvTagVol());
                    outside.put("name",threeSrvTag.getSrvTag());
                    outPie.add(outside);
                }
            }
        }
        threeSrvTagPie.put("insidePie",inPie);
        threeSrvTagPie.put("outsidePie",outPie);

        return R.ok(threeSrvTagPie);


    }

    /**
     * 三服务活动
     * 修改为定向辅导
     */
//    @ApiOperation("定向辅导")
    @GetMapping("/three-srv-act")
    @ResponseBody
    public R<List<QscThreeSrvActVo>> threeSrvAct() {
        QscThreeSrvActBo threeSrvActBo = new QscThreeSrvActBo();
        List<QscThreeSrvActVo> threeTagAct = threeSrvActService.queryList(threeSrvActBo);
        for (QscThreeSrvActVo threeSrvAct : threeTagAct) {
            threeSrvAct.setActContent(HtmlUtil.cleanHtmlTag(threeSrvAct.getActContent()));
            // 转换图片 OSS ID 为 URL
            if (StringUtils.isNotEmpty(threeSrvAct.getActImage())) {
                String[] images = threeSrvAct.getActImage().split(",");
                List<String> urlsList = Arrays.stream(images)
                    .map(image -> {
                        // 如果已经是 URL 格式，直接返回
                        if (image.toLowerCase().startsWith("http://") || 
                            image.toLowerCase().startsWith("https://")) {
                            return image;
                        }
                        // 否则尝试转换 OSS ID 为 URL
                        try {
                            SysOssVo oss = ossService.getById(Long.parseLong(image.trim()));
                            return oss != null ? oss.getUrl() : null;
                        } catch (Exception e) {
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
                threeSrvAct.setActImage(String.join(",", urlsList));
            }
        }
        return R.ok(threeTagAct);
    }

    /**
     * 好差评
     */
//    @ApiOperation("好差评率")
    @GetMapping("/rank-rate")
    @ResponseBody
    public R<Map<String,Object>> rankRate() {
        Map<String,Object> rankRate = new HashMap<>();
        QscTaxRankRateVo rankRateVo = rankRateService.queryById(1L);

        if (null!=rankRateVo){
            rankRate.put("bestRankCount",rankRateVo.getBestRank());
            rankRate.put("badRankCount",rankRateVo.getBadRank());
//            rankRate.put("bestRankCount",rankRateVo.getBadRank());
//            rankRate.put("badRankCount",rankRateVo.getBestRank());

        }
        return R.ok(rankRate);
    }

    /**
     * 好差评渠道
     */
//    @ApiOperation("好差评渠道")
    @GetMapping("/rank-channel")
    @ResponseBody
    public R<List<PathCharts>> rankChannel() {
        List<QscTaxRankChannelVo> rankChannelVos = rankChannelService.queryList(new QscTaxRankChannelBo());
        List<PathCharts> pathList = new ArrayList<>();

        if(null!= rankChannelVos){
            long Max = 0;
            for (QscTaxRankChannelVo rankChannelVo :rankChannelVos) {
                Max += rankChannelVo.getRankVol();
            }
            for (QscTaxRankChannelVo rankChannel : rankChannelVos){
                PathCharts p = new PathCharts();
                p.setLeftText(rankChannel.getRankChannelName());
                p.setMax(Max);
                p.setNum(rankChannel.getRankVol());
                pathList.add(p);
            }
        }
        return R.ok(pathList);
    }

    /**
     * 好差评标签
     */
//    @ApiOperation("好差评标签")
    @GetMapping("/rank-tag")
    @ResponseBody
    public R<RadarCharts> rankTag() {
        RadarCharts radar = new RadarCharts();

        List<QscTaxRankTagVo> tagBos = tagService.queryList(new QscTaxRankTagBo());
        if(null!=tagBos){
            List<RadarChartsData> radarData = new ArrayList<>();
            List<Map<String,Object>> indicator = new ArrayList<>();
            List<Long> jsList = new ArrayList<>();
            List<Long> dmList = new ArrayList<>();
            List<Long> jhList = new ArrayList<>();
            List<Long> bpList = new ArrayList<>();

            Integer max = tagService.maxRankVal();

            for (QscTaxRankTagVo vo: tagBos){
                Map<String,Object> indicatorMap = new HashMap<>();
                indicatorMap.put("name",vo.getTagName());
                indicatorMap.put("max",max);
                indicator.add(indicatorMap);
                dmList.add(vo.getTaxHallOne());
                jsList.add(vo.getTaxHallTwo());
                jhList.add(vo.getTaxHallThr());
                bpList.add(vo.getBizPer());
            }

//            RadarChartsData dmData = new RadarChartsData(dmList,"斗门办税大厅","none",new AreaStyle("yellow",0.45));
            RadarChartsData jsData = new RadarChartsData(jsList,"稽山办税大厅","none",new AreaStyle("#33FFFF",0.45));
            RadarChartsData jhData = new RadarChartsData(jhList,"鉴湖办税大厅","none",new AreaStyle("green",0.45));
            RadarChartsData bpData = new RadarChartsData(bpList,"个人业务","none",new AreaStyle("purple",0.45));
//            radarData.add(dmData);
            radarData.add(jsData);
            radarData.add(jhData);
            radarData.add(bpData);

            radar.setIndicator(indicator);
            radar.setData(radarData);
        }
        return R.ok(radar);
    }



    /**
     * 征那纳沟通平台
     */
//    @ApiOperation("征那纳沟通平台")
    @GetMapping("/cost-common-data")
    @ResponseBody
    public R<QscTaxCommDataVo> costCommData() {
        return R.ok(commDataService.queryById(1L));
    }


    /**
     *
     */
    // @GetMapping("/monthly-report")
    // @ResponseBody
    // public R<MonthlyReport> monthlyReport() {
    //     QscTaxSentimentMonthlyReportVo vo = reportService.queryById(1L);
    //     MonthlyReport report = new MonthlyReport();

    //     if (StringUtils.isNotEmpty(vo.getImageUrls())) {
    //         String[] ossIds = vo.getImageUrls().split(",");
    //         List<String> urlsList = new ArrayList<>();
    //         for (String ossId : ossIds) {
    //             String url = ossService.selectUrlByOssId(Long.parseLong(ossId));
    //             if (StringUtils.isNotEmpty(url)) {
    //                 urlsList.add(url);
    //             }
    //         }
    //         report.setImageUrls(urlsList);
    //     }

    //     report.setContent(HtmlUtil.cleanHtmlTag(vo.getTextContent()));
    //     return R.ok(report);
    // }
    @GetMapping("/monthly-report")
    @ResponseBody
    public R<MonthlyReport> monthlyReport() {
        QscTaxSentimentMonthlyReportVo vo = reportService.queryById(1L);
        MonthlyReport report = new MonthlyReport();

        if (StringUtils.isNotEmpty(vo.getImageUrls())) {
            String[] ossIds = vo.getImageUrls().split(",");
            List<String> urlsList = Arrays.stream(ossIds)
                .map(ossId -> {
                    // 通过 ossId 获取 SysOssVo 实体
                    SysOssVo oss = ossService.getById(Long.parseLong(ossId));
                    return oss != null ? oss.getUrl() : null;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
            report.setImageUrls(urlsList);
        }

        report.setContent(HtmlUtil.cleanHtmlTag(vo.getTextContent()));
        return R.ok(report);
    }
    @Data
    class MonthlyReport {
        private List<String> imageUrls;
        private String content;
    }
}
