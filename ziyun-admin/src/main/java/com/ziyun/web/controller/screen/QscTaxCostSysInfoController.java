package com.ziyun.web.controller.screen;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ziyun.common.annotation.Log;
import com.ziyun.common.annotation.RepeatSubmit;
import com.ziyun.common.core.controller.BaseController;
import com.ziyun.common.core.domain.PageQuery;
import com.ziyun.common.core.domain.R;
import com.ziyun.common.core.page.TableDataInfo;
import com.ziyun.common.core.validate.AddGroup;
import com.ziyun.common.core.validate.EditGroup;
import com.ziyun.common.enums.BusinessType;
import com.ziyun.common.utils.poi.ExcelUtil;
import com.ziyun.screen.domain.bo.QscTaxCostSysInfoBo;
import com.ziyun.screen.domain.vo.QscTaxCostSysInfoVo;
import com.ziyun.screen.service.IQscTaxCostSysInfoService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import java.util.Arrays;
import java.util.List;

/**
 * 税费协同信息
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/screen/taxCostSysInfo")
public class QscTaxCostSysInfoController extends BaseController {

    private final IQscTaxCostSysInfoService iQscTaxCostSysInfoService;

    /**
     * 查询税费协同信息列表
     */
    @SaCheckPermission("screen:taxCostSysInfo:list")
    @GetMapping("/list")
    public TableDataInfo<QscTaxCostSysInfoVo> list(QscTaxCostSysInfoBo bo, PageQuery pageQuery) {
        return iQscTaxCostSysInfoService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出税费协同信息列表
     */
    @SaCheckPermission("screen:taxCostSysInfo:export")
    @Log(title = "税费协同信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(QscTaxCostSysInfoBo bo, HttpServletResponse response) {
        List<QscTaxCostSysInfoVo> list = iQscTaxCostSysInfoService.queryList(bo);
        ExcelUtil.exportExcel(list, "税费协同信息", QscTaxCostSysInfoVo.class, response);
    }

    /**
     * 获取税费协同信息详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("screen:taxCostSysInfo:query")
    @GetMapping("/{id}")
    public R<QscTaxCostSysInfoVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iQscTaxCostSysInfoService.queryById(id));
    }

    /**
     * 新增税费协同信息
     */
    @SaCheckPermission("screen:taxCostSysInfo:add")
    @Log(title = "税费协同信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody QscTaxCostSysInfoBo bo) {
        return toAjax(iQscTaxCostSysInfoService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改税费协同信息
     */
    @SaCheckPermission("screen:taxCostSysInfo:edit")
    @Log(title = "税费协同信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody QscTaxCostSysInfoBo bo) {
        return toAjax(iQscTaxCostSysInfoService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除税费协同信息
     *
     * @param ids 主键串
     */
    @SaCheckPermission("screen:taxCostSysInfo:remove")
    @Log(title = "税费协同信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iQscTaxCostSysInfoService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
