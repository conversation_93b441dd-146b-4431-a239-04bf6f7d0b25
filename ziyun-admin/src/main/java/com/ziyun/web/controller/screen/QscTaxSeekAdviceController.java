package com.ziyun.web.controller.screen;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ziyun.common.annotation.Log;
import com.ziyun.common.annotation.RepeatSubmit;
import com.ziyun.common.core.controller.BaseController;
import com.ziyun.common.core.domain.PageQuery;
import com.ziyun.common.core.domain.R;
import com.ziyun.common.core.page.TableDataInfo;
import com.ziyun.common.core.validate.AddGroup;
import com.ziyun.common.core.validate.EditGroup;
import com.ziyun.common.enums.BusinessType;
import com.ziyun.common.utils.poi.ExcelUtil;
import com.ziyun.screen.domain.bo.QscTaxSeekAdviceBo;
import com.ziyun.screen.domain.vo.QscTaxSeekAdviceVo;
import com.ziyun.screen.service.IQscTaxSeekAdviceService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import java.util.Arrays;
import java.util.List;

/**
 * 咨询热点
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/screen/taxSeekAdvice")
public class QscTaxSeekAdviceController extends BaseController {

    private final IQscTaxSeekAdviceService iQscTaxSeekAdviceService;

    /**
     * 查询咨询热点列表
     */
    @SaCheckPermission("screen:taxSeekAdvice:list")
    @GetMapping("/list")
    public TableDataInfo<QscTaxSeekAdviceVo> list(QscTaxSeekAdviceBo bo, PageQuery pageQuery) {
        return iQscTaxSeekAdviceService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出咨询热点列表
     */
    @SaCheckPermission("screen:taxSeekAdvice:export")
    @Log(title = "咨询热点", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(QscTaxSeekAdviceBo bo, HttpServletResponse response) {
        List<QscTaxSeekAdviceVo> list = iQscTaxSeekAdviceService.queryList(bo);
        ExcelUtil.exportExcel(list, "咨询热点", QscTaxSeekAdviceVo.class, response);
    }

    /**
     * 获取咨询热点详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("screen:taxSeekAdvice:query")
    @GetMapping("/{id}")
    public R<QscTaxSeekAdviceVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iQscTaxSeekAdviceService.queryById(id));
    }

    /**
     * 新增咨询热点
     */
    @SaCheckPermission("screen:taxSeekAdvice:add")
    @Log(title = "咨询热点", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody QscTaxSeekAdviceBo bo) {
        return toAjax(iQscTaxSeekAdviceService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改咨询热点
     */
    @SaCheckPermission("screen:taxSeekAdvice:edit")
    @Log(title = "咨询热点", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody QscTaxSeekAdviceBo bo) {
        return toAjax(iQscTaxSeekAdviceService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除咨询热点
     *
     * @param ids 主键串
     */
    @SaCheckPermission("screen:taxSeekAdvice:remove")
    @Log(title = "咨询热点", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iQscTaxSeekAdviceService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
