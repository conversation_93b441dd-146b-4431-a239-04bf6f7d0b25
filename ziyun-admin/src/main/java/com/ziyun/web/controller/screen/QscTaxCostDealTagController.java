package com.ziyun.web.controller.screen;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ziyun.common.annotation.Log;
import com.ziyun.common.annotation.RepeatSubmit;
import com.ziyun.common.core.controller.BaseController;
import com.ziyun.common.core.domain.PageQuery;
import com.ziyun.common.core.domain.R;
import com.ziyun.common.core.page.TableDataInfo;
import com.ziyun.common.core.validate.AddGroup;
import com.ziyun.common.core.validate.EditGroup;
import com.ziyun.common.enums.BusinessType;
import com.ziyun.common.utils.poi.ExcelUtil;
import com.ziyun.screen.domain.bo.QscTaxCostDealTagBo;
import com.ziyun.screen.domain.vo.QscTaxCostDealTagVo;
import com.ziyun.screen.service.IQscTaxCostDealTagService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import java.util.Arrays;
import java.util.List;

/**
 * 税费协同工单标签
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/screen/taxCostDealTag")
public class QscTaxCostDealTagController extends BaseController {

    private final IQscTaxCostDealTagService iQscTaxCostDealTagService;

    /**
     * 查询税费协同工单标签列表
     */
    @SaCheckPermission("screen:taxCostDealTag:list")
    @GetMapping("/list")
    public TableDataInfo<QscTaxCostDealTagVo> list(QscTaxCostDealTagBo bo, PageQuery pageQuery) {
        return iQscTaxCostDealTagService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出税费协同工单标签列表
     */
    @SaCheckPermission("screen:taxCostDealTag:export")
    @Log(title = "税费协同工单标签", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(QscTaxCostDealTagBo bo, HttpServletResponse response) {
        List<QscTaxCostDealTagVo> list = iQscTaxCostDealTagService.queryList(bo);
        ExcelUtil.exportExcel(list, "税费协同工单标签", QscTaxCostDealTagVo.class, response);
    }

    /**
     * 获取税费协同工单标签详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("screen:taxCostDealTag:query")
    @GetMapping("/{id}")
    public R<QscTaxCostDealTagVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iQscTaxCostDealTagService.queryById(id));
    }

    /**
     * 新增税费协同工单标签
     */
    @SaCheckPermission("screen:taxCostDealTag:add")
    @Log(title = "税费协同工单标签", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody QscTaxCostDealTagBo bo) {
        return toAjax(iQscTaxCostDealTagService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改税费协同工单标签
     */
    @SaCheckPermission("screen:taxCostDealTag:edit")
    @Log(title = "税费协同工单标签", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody QscTaxCostDealTagBo bo) {
        return toAjax(iQscTaxCostDealTagService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除税费协同工单标签
     *
     * @param ids 主键串
     */
    @SaCheckPermission("screen:taxCostDealTag:remove")
    @Log(title = "税费协同工单标签", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iQscTaxCostDealTagService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
