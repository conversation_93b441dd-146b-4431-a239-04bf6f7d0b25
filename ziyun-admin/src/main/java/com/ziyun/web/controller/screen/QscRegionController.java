package com.ziyun.web.controller.screen;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ziyun.common.annotation.Log;
import com.ziyun.common.annotation.RepeatSubmit;
import com.ziyun.common.core.controller.BaseController;
import com.ziyun.common.core.domain.PageQuery;
import com.ziyun.common.core.domain.R;
import com.ziyun.common.core.page.TableDataInfo;
import com.ziyun.common.core.validate.AddGroup;
import com.ziyun.common.core.validate.EditGroup;
import com.ziyun.common.enums.BusinessType;
import com.ziyun.common.utils.poi.ExcelUtil;
import com.ziyun.screen.domain.bo.QscRegionBo;
import com.ziyun.screen.domain.vo.QscRegionVo;
import com.ziyun.screen.service.IQscRegionService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import java.util.Arrays;
import java.util.List;

/**
 * 地区
 *
 * <AUTHOR>
 * @date 2022-11-08
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/screen/region")
public class QscRegionController extends BaseController {

    private final IQscRegionService iQscRegionService;

    /**
     * 查询地区列表
     */
    @SaCheckPermission("screen:region:list")
    @GetMapping("/list")
    public TableDataInfo<QscRegionVo> list(QscRegionBo bo, PageQuery pageQuery) {
        return iQscRegionService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出地区列表
     */
    @SaCheckPermission("screen:region:export")
    @Log(title = "地区", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(QscRegionBo bo, HttpServletResponse response) {
        List<QscRegionVo> list = iQscRegionService.queryList(bo);
        ExcelUtil.exportExcel(list, "地区", QscRegionVo.class, response);
    }

    /**
     * 获取地区详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("screen:region:query")
    @GetMapping("/{id}")
    public R<QscRegionVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iQscRegionService.queryById(id));
    }

    /**
     * 新增地区
     */
    @SaCheckPermission("screen:region:add")
    @Log(title = "地区", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody QscRegionBo bo) {
        return toAjax(iQscRegionService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改地区
     */
    @SaCheckPermission("screen:region:edit")
    @Log(title = "地区", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody QscRegionBo bo) {
        return toAjax(iQscRegionService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除地区
     *
     * @param ids 主键串
     */
    @SaCheckPermission("screen:region:remove")
    @Log(title = "地区", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iQscRegionService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
