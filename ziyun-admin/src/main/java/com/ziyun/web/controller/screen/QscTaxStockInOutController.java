package com.ziyun.web.controller.screen;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ziyun.common.annotation.Log;
import com.ziyun.common.annotation.RepeatSubmit;
import com.ziyun.common.core.controller.BaseController;
import com.ziyun.common.core.domain.PageQuery;
import com.ziyun.common.core.domain.R;
import com.ziyun.common.core.page.TableDataInfo;
import com.ziyun.common.core.validate.AddGroup;
import com.ziyun.common.core.validate.EditGroup;
import com.ziyun.common.enums.BusinessType;
import com.ziyun.common.excel.ExcelResult;
import com.ziyun.common.utils.poi.ExcelUtil;
import com.ziyun.screen.domain.bo.QscTaxStockInOutBo;
import com.ziyun.screen.domain.vo.QscTaxStockInOutImportVo;
import com.ziyun.screen.domain.vo.QscTaxStockInOutVo;
import com.ziyun.screen.listener.QscTaxStockInOutImportListener;
import com.ziyun.screen.service.IQscTaxStockInOutService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 入库出库
 *
 * <AUTHOR>
 * @date 2022-12-08
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/screen/taxStockInOut")
public class QscTaxStockInOutController extends BaseController {

    private final IQscTaxStockInOutService iQscTaxStockInOutService;

    /**
     * 查询入库出库列表
     */
    @SaCheckPermission("screen:taxStockInOut:list")
    @GetMapping("/list")
    public TableDataInfo<QscTaxStockInOutVo> list(QscTaxStockInOutBo bo, PageQuery pageQuery) {
        return iQscTaxStockInOutService.queryPageList(bo, pageQuery);
    }

    /**
     * 导入入库出库数据
     *
     * @param file 导入文件
     */
    @Log(title = "入库出库", businessType = BusinessType.IMPORT)
    @SaCheckPermission("screen:taxStockInOut:import")
    @PostMapping(value = "/importData", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<Void> importData(@RequestPart("file") MultipartFile file, boolean updateSupport) throws Exception {
        ExcelResult<QscTaxStockInOutImportVo> result = ExcelUtil.importExcel(file.getInputStream(), QscTaxStockInOutImportVo.class, new QscTaxStockInOutImportListener(updateSupport));
        return R.ok(result.getAnalysis());
    }

    /**
     * 导出入库出库列表
     */
    @SaCheckPermission("screen:taxStockInOut:export")
    @Log(title = "入库出库", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(QscTaxStockInOutBo bo, HttpServletResponse response) {
        List<QscTaxStockInOutVo> list = iQscTaxStockInOutService.queryList(bo);
        ExcelUtil.exportExcel(list, "入库出库", QscTaxStockInOutVo.class, response);
    }

    /**
     * 获取入库出库详细信息
     *
     * @param stockId 主键
     */
    @SaCheckPermission("screen:taxStockInOut:query")
    @GetMapping("/{stockId}")
    public R<QscTaxStockInOutVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long stockId) {
        return R.ok(iQscTaxStockInOutService.queryById(stockId));
    }

    /**
     * 新增入库出库
     */
    @SaCheckPermission("screen:taxStockInOut:add")
    @Log(title = "入库出库", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody QscTaxStockInOutBo bo) {
        return toAjax(iQscTaxStockInOutService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改入库出库
     */
    @SaCheckPermission("screen:taxStockInOut:edit")
    @Log(title = "入库出库", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody QscTaxStockInOutBo bo) {
        return toAjax(iQscTaxStockInOutService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除入库出库
     *
     * @param stockIds 主键串
     */
    @SaCheckPermission("screen:taxStockInOut:remove")
    @Log(title = "入库出库", businessType = BusinessType.DELETE)
    @DeleteMapping("/{stockIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] stockIds) {
        return toAjax(iQscTaxStockInOutService.deleteWithValidByIds(Arrays.asList(stockIds), true) ? 1 : 0);
    }

    /**
     * 获取导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil.exportExcel(new ArrayList<>(), "用户数据", QscTaxStockInOutImportVo.class, response);
    }
}
