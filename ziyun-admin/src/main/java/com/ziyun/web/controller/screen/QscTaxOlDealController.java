package com.ziyun.web.controller.screen;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ziyun.common.annotation.Log;
import com.ziyun.common.annotation.RepeatSubmit;
import com.ziyun.common.core.controller.BaseController;
import com.ziyun.common.core.domain.PageQuery;
import com.ziyun.common.core.domain.R;
import com.ziyun.common.core.page.TableDataInfo;
import com.ziyun.common.core.validate.AddGroup;
import com.ziyun.common.core.validate.EditGroup;
import com.ziyun.common.enums.BusinessType;
import com.ziyun.common.utils.poi.ExcelUtil;
import com.ziyun.screen.domain.bo.QscTaxOlDealBo;
import com.ziyun.screen.domain.vo.QscTaxOlDealVo;
import com.ziyun.screen.service.IQscTaxOlDealService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import java.util.Arrays;
import java.util.List;

/**
 * 网上办
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/screen/taxOlDeal")
public class QscTaxOlDealController extends BaseController {

    private final IQscTaxOlDealService iQscTaxOlDealService;

    /**
     * 查询网上办列表
     */
    @SaCheckPermission("screen:taxOlDeal:list")
    @GetMapping("/list")
    public TableDataInfo<QscTaxOlDealVo> list(QscTaxOlDealBo bo, PageQuery pageQuery) {
        return iQscTaxOlDealService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出网上办列表
     */
    @SaCheckPermission("screen:taxOlDeal:export")
    @Log(title = "网上办", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(QscTaxOlDealBo bo, HttpServletResponse response) {
        List<QscTaxOlDealVo> list = iQscTaxOlDealService.queryList(bo);
        ExcelUtil.exportExcel(list, "网上办", QscTaxOlDealVo.class, response);
    }

    /**
     * 获取网上办详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("screen:taxOlDeal:query")
    @GetMapping("/{id}")
    public R<QscTaxOlDealVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iQscTaxOlDealService.queryById(id));
    }

    /**
     * 新增网上办
     */
    @SaCheckPermission("screen:taxOlDeal:add")
    @Log(title = "网上办", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody QscTaxOlDealBo bo) {
        return toAjax(iQscTaxOlDealService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改网上办
     */
    @SaCheckPermission("screen:taxOlDeal:edit")
    @Log(title = "网上办", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody QscTaxOlDealBo bo) {
        return toAjax(iQscTaxOlDealService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除网上办
     *
     * @param ids 主键串
     */
    @SaCheckPermission("screen:taxOlDeal:remove")
    @Log(title = "网上办", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iQscTaxOlDealService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
