package com.ziyun.web.controller.screen;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ziyun.common.annotation.Log;
import com.ziyun.common.annotation.RepeatSubmit;
import com.ziyun.common.core.controller.BaseController;
import com.ziyun.common.core.domain.PageQuery;
import com.ziyun.common.core.domain.R;
import com.ziyun.common.core.page.TableDataInfo;
import com.ziyun.common.core.validate.AddGroup;
import com.ziyun.common.core.validate.EditGroup;
import com.ziyun.common.enums.BusinessType;
import com.ziyun.common.utils.poi.ExcelUtil;
import com.ziyun.screen.domain.bo.QscTaxRegionIndustryTagInfoBo;
import com.ziyun.screen.domain.vo.QscTaxRegionIndustryTagInfoVo;
import com.ziyun.screen.service.IQscTaxRegionIndustryTagInfoService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import java.util.Arrays;
import java.util.List;

/**
 * 行业分类
 *
 * <AUTHOR>
 * @date 2022-11-08
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/screen/taxRegionIndustryTagInfo")
public class QscTaxRegionIndustryTagInfoController extends BaseController {

    private final IQscTaxRegionIndustryTagInfoService iQscTaxRegionIndustryTagInfoService;

    /**
     * 查询行业分类列表
     */
    @SaCheckPermission("screen:taxRegionIndustryTagInfo:list")
    @GetMapping("/list")
    public TableDataInfo<QscTaxRegionIndustryTagInfoVo> list(QscTaxRegionIndustryTagInfoBo bo, PageQuery pageQuery) {
        return iQscTaxRegionIndustryTagInfoService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出行业分类列表
     */
    @SaCheckPermission("screen:taxRegionIndustryTagInfo:export")
    @Log(title = "行业分类", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(QscTaxRegionIndustryTagInfoBo bo, HttpServletResponse response) {
        List<QscTaxRegionIndustryTagInfoVo> list = iQscTaxRegionIndustryTagInfoService.queryList(bo);
        ExcelUtil.exportExcel(list, "行业分类", QscTaxRegionIndustryTagInfoVo.class, response);
    }

    /**
     * 获取行业分类详细信息
     *
     * @param regionId 主键
     */
    @SaCheckPermission("screen:taxRegionIndustryTagInfo:query")
    @GetMapping("/{regionId}")
    public R<QscTaxRegionIndustryTagInfoVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long regionId) {
        return R.ok(iQscTaxRegionIndustryTagInfoService.queryById(regionId));
    }

    /**
     * 新增行业分类
     */
    @SaCheckPermission("screen:taxRegionIndustryTagInfo:add")
    @Log(title = "行业分类", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody QscTaxRegionIndustryTagInfoBo bo) {
        return toAjax(iQscTaxRegionIndustryTagInfoService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改行业分类
     */
    @SaCheckPermission("screen:taxRegionIndustryTagInfo:edit")
    @Log(title = "行业分类", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody QscTaxRegionIndustryTagInfoBo bo) {
        return toAjax(iQscTaxRegionIndustryTagInfoService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除行业分类
     *
     * @param regionIds 主键串
     */
    @SaCheckPermission("screen:taxRegionIndustryTagInfo:remove")
    @Log(title = "行业分类", businessType = BusinessType.DELETE)
    @DeleteMapping("/{regionIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] regionIds) {
        return toAjax(iQscTaxRegionIndustryTagInfoService.deleteWithValidByIds(Arrays.asList(regionIds), true) ? 1 : 0);
    }
}
