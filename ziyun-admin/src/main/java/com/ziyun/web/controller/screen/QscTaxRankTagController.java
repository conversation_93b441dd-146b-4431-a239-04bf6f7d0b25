package com.ziyun.web.controller.screen;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ziyun.common.annotation.Log;
import com.ziyun.common.annotation.RepeatSubmit;
import com.ziyun.common.core.controller.BaseController;
import com.ziyun.common.core.domain.PageQuery;
import com.ziyun.common.core.domain.R;
import com.ziyun.common.core.page.TableDataInfo;
import com.ziyun.common.core.validate.AddGroup;
import com.ziyun.common.core.validate.EditGroup;
import com.ziyun.common.enums.BusinessType;
import com.ziyun.common.utils.poi.ExcelUtil;
import com.ziyun.screen.domain.bo.QscTaxRankTagBo;
import com.ziyun.screen.domain.vo.QscTaxRankTagVo;
import com.ziyun.screen.service.IQscTaxRankTagService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import java.util.Arrays;
import java.util.List;

/**
 * 好差评标签
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/screen/taxRankTag")
public class QscTaxRankTagController extends BaseController {

    private final IQscTaxRankTagService iQscTaxRankTagService;

    /**
     * 查询好差评标签列表
     */
    @SaCheckPermission("screen:taxRankTag:list")
    @GetMapping("/list")
    public TableDataInfo<QscTaxRankTagVo> list(QscTaxRankTagBo bo, PageQuery pageQuery) {
        return iQscTaxRankTagService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出好差评标签列表
     */
    @SaCheckPermission("screen:taxRankTag:export")
    @Log(title = "好差评标签", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(QscTaxRankTagBo bo, HttpServletResponse response) {
        List<QscTaxRankTagVo> list = iQscTaxRankTagService.queryList(bo);
        ExcelUtil.exportExcel(list, "好差评标签", QscTaxRankTagVo.class, response);
    }

    /**
     * 获取好差评标签详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("screen:taxRankTag:query")
    @GetMapping("/{id}")
    public R<QscTaxRankTagVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iQscTaxRankTagService.queryById(id));
    }

    /**
     * 新增好差评标签
     */
    @SaCheckPermission("screen:taxRankTag:add")
    @Log(title = "好差评标签", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody QscTaxRankTagBo bo) {
        return toAjax(iQscTaxRankTagService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改好差评标签
     */
    @SaCheckPermission("screen:taxRankTag:edit")
    @Log(title = "好差评标签", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody QscTaxRankTagBo bo) {
        return toAjax(iQscTaxRankTagService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除好差评标签
     *
     * @param ids 主键串
     */
    @SaCheckPermission("screen:taxRankTag:remove")
    @Log(title = "好差评标签", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iQscTaxRankTagService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
